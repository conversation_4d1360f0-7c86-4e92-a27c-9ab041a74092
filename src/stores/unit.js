import {defineStore} from 'pinia'
import {ref} from 'vue'
import {pubStore} from './pub'
const pub = pubStore()
import {useRoute} from 'vue-router'

const Service = App.service('unit')
import {QuestionTypes} from '../boot/const'

export const unitStore = defineStore('unit', {
  state: () => ({
    one: ref(null),
    isLib: ref(false),
    pageId: ref(null),
    outline: ref(null),
    questions: ref(null),
    materials: ref(null),
    colorMap: ref({}),
    iconMap: ref({}),
    _caches: ref({}),
    collab: ref(null),
  }),
  getters: {
    isOwner() {
      // 是否创建人
      return this.one.uid === pub.user._id
    },
    isUnit() {
      return this.one.mode.toLowerCase().includes('unit')
    },
    isTask() {
      return this.one.mode.toLowerCase().includes('task')
    },
    isTool() {
      return this.one.mode.toLowerCase().includes('tool')
    },
    isVideo() {
      return this.one.mode.toLowerCase().includes('video')
    },
    member() {
      if (!this.collab) return null
      return this.collab.members.find((v) => v.email === pub.user.email)
    },
    linkGroup() {
      return this.one.linkGroup.map((group) => {
        return {...group, link: this.one.link.filter((v) => v.group === group._id)}
      })
    },
    // temp
    page() {
      return this.one.pages.find((v) => v._id === this.pageId)
    },
    question() {
      return this._getQuestion(this.questions, this.pageId)
    },
    tagTypes() {
      return ['assess', 'outline', 'skills', 'goal']
    },
    numbersOfOutline() {
      return this._getNumbersOfOutline(this.tagTypes, this.question)
    },
    material() {
      return this._getMaterial(this.materials, this.pageId)
    },
  },
  actions: {
    async getCollab() {
      this.collab = await App.service('collab').get(this.one._id, {query: {type: this.one.mode}})
    },
    viewRoute(doc, pid, type) {
      const route = useRoute()
      const prr = ['', 'unit']
      if (!this.isLib) prr.push('my')
      if (type) prr.push(type)
      if (this.isLib && pid) prr.push(pid)
      prr.push(doc._id)
      let path = prr.join('/')
      if (route.path.includes('/auth/') && route.params.authId) {
        return {path: `${route.path}/${doc._id}`, query: {back: route.fullPath}}
      }
      return {path, query: {back: route.fullPath}}
    },
    editRoute(doc, route, query = {}) {
      route = route || useRoute()
      let path = `/com/${doc.mode.indexOf('nit') > -1 ? 'unit' : 'task'}/edit/${doc._id}`
      if (doc.mode === 'tool') path = `/account/assessment-tool/${doc._id}`
      else if (doc.mode === 'video') path = `/video/edit/${doc._id}`
      return {path, query: {...query, back: route.fullPath}}
    },
    scheduleRoute(doc) {
      const route = useRoute()
      return {
        path: doc.mode == 'tool' ? `/com/schedule/student/${doc._id}` : `/com/${doc.mode}/edit/${doc._id}`,
        query: {action: 'schedule', back: route.fullPath},
      }
    },
    _getTitle(doc) {
      const modeMap = {
        unit: 'Premium lecture',
        task: 'Premium task',
        video: 'Premium task',
        pdUnit: 'Premium lecture',
        pdTask: 'Premium task',
        pdTaskeducators: 'Teaching resource',
        pdTaskstudents: 'Workshop',
        tool: 'Assessment tool',
      }
      return modeMap[doc.mode] || ''
      // return modeMap[`${doc.mode}${doc.service?.participants ?? ''}`] || `${doc.mode}${doc.service?.participants}`
    },
    _getMaterial(materials, pageId) {
      if (!materials) return {}
      const rs = materials.find((v) => v.page === pageId)
      if (!rs) return {}
      return rs
    },
    _getQuestion(questions, pageId) {
      const defRs = {title: 'No Question', icon: 'block', color: 'grey-10'}
      if (!questions) return defRs
      const o = questions.find((v) => v.page === pageId)
      if (!o) return defRs
      Object.assign(o, QuestionTypes[o.type])
      return o
    },
    _outlineCount(data) {
      let num = 0
      for (const o of data) {
        if (!Acan.isEmpty(o.child)) num += this._outlineCount(o.child)
        else num++
      }
      return num
    },
    _getNumbersOfOutline(tagTypes, question) {
      const rs = {}
      for (const tag of tagTypes) {
        if (!question.outlines) continue
        rs[tag] = this._outlineCount(question.outlines[tag])
      }
      return rs
    },
    async create(post) {
      const doc = await Service.create(post)
      return doc
    },
    async patch(_id, post) {
      const doc = await Service.patch(_id, post)
      this._caches = {}
      return doc
    },
    async delete(_id) {
      const doc = await Service.remove(_id)
      this._caches = {}
      return doc
    },
    async find(query) {
      return await Service.find({query})
    },
    async relateLinkList(rid = '') {
      return await Service.get('relateLinkList', {query: {rid}})
    },
    // batch find
    async allRelateLinkList(rid = []) {
      return await Service.get('allRelateLinkList', {query: {rid}})
    },
    async copyTool({_id, unit, name}) {
      return await App.service('unit').get('copyTool', {query: {_id, unit, name}})
    },
    async search() {
      return await App.service('unit').get('search', {query: {key: pub.search, limit: 10}})
    },
    errorFn() {
      return
    },
    async setAuthUnit(doc, linkSnapshot) {
      this.clear()
      this.one = doc
      this.outline = doc.outline
      this.materials = doc.materials
      this.questions = doc.questions
      if (linkSnapshot) {
        this.one.link.map((v) => {
          v.doc = linkSnapshot[v.id]
          if (!v.doc) return
          v.doc.owner = this.one.owner
        })
      }
    },
    clear() {
      this.pageId = null
      this.one = null
      this.collab = null
      this.outline = null
      this.questions = null
      this.materials = null
    },
    async getLib(_id, linkId) {
      let doc
      if (this._caches[_id]) doc = this._caches[_id]
      else {
        doc = await Service.get(_id, {query: {isLib: true}}).catch(this.errorFn)
        this._caches[_id] = doc
      }
      // library 课件
      this.clear()
      this.isLib = true
      this.one = linkId ? doc.linkSnapshot[linkId] : doc
      this.one.owner = doc.owner
      this.outline = this.one.outline
      this.materials = this.one.materials
      this.questions = this.one.questions

      if (doc.linkSnapshot) {
        this.one.link.map((v) => {
          v.doc = doc.linkSnapshot[v.id]
          if (!v.doc) return
          v.doc.owner = this.one.owner
        })
      }
    },
    async get(_id, getAll = false) {
      if (!Acan.isObjectId(_id)) return
      this.clear()
      this.one = await Service.get(_id, {query: {isLib: false}}).catch(this.errorFn)
      if (!this.one) return
      if (this.isUnit) {
        this.outline = await App.service('task-outline').get('byRid', {query: {_id: this.one._id}})
        this.getLinkName(false)
      } else if (this.one.sid?.includes('hash:')) {
        // 还未初始化的使用快照
        this.questions = this.one.snapshot?.questions
        this.materials = this.one.snapshot?.materials
        this.outline = this.one.snapshot?.outline
      } else if (this.one.sid === 'disable') {
        // 协同课件副本
        this.questions = this.one.snapshot?.questions
        this.materials = this.one.snapshot?.materials
        this.outline = await App.service('task-outline').get('byRid', {query: {_id: this.one._id}})
      } else {
        // 自己编辑的课件
        await this.getAll(getAll)
      }
      return this.one
    },
    async getLinkName(isLib) {
      const $in = this.one.link.map((v) => v.id)
      const {data} = await Service.find({query: {tab: isLib ? 'lib' : 'me', _id: $in}})
      // const lrr = await Service.get('linksById', {query: {id: , isLib}})
      const lmap = {}
      data.map((v) => {
        lmap[v._id] = v
      })
      this.one.link.map((v) => {
        v.doc = lmap[v.id]
      })
    },
    async getAll(all = false) {
      if (this.one.sid?.includes('hash:')) return // 复制的课件从快照获取
      this.outline = await App.service('task-outline').get('byRid', {query: {_id: this.one._id}})
      if (!this.one.sid) return
      if (!all) return
      await this.getLinkName(false)
      // let rs = await App.service('slides').get('bySlideId', {query: {id: this.one.sid}})
      // if (!rs) return
      // this.pages = rs.pages
      let {data} = await App.service('questions').find({query: {id: this.one.sid, $limit: 1000}})
      this.questions = data
      const mrs = await App.service('materials').find({query: {id: this.one.sid, $limit: 1000}})
      this.materials = mrs.data
    },
  },
})
