export default {
  path: '/detail/',
  component: () => import('layouts/DetailLayout.vue'),
  children: [
    {path: 'booking/:id', component: () => import('pages/booking/packageDetailPage.vue?p=1')},
    {path: 'booking/:tab/:id', component: () => import('pages/booking/packageDetailPage.vue?p=2')},
    {path: 'overview/:state/:id', component: () => import('pages/OverviewPage.vue')},
    {path: 'content/:tab/:id', component: () => import('pages/ExplicitPage.vue?p=1')},
    {path: ':type/:id', component: () => import('pages/ExplicitPage.vue?p=2')},
    {path: ':type/:id/review', component: () => import('pages/ReviewPage.vue')},
    {path: ':type/limit/:id', component: () => import('pages/ExplicitPage.vue?p=3')},
    {path: ':type/:id/:sid', component: () => import('pages/ExplicitPage.vue?p=4')},
    {path: 'course/:pid/session/:id', component: () => import('pages/ExplicitPage.vue?p=5')},
    {path: ':type/import/:id', component: () => import('pages/ImportPage.vue')},
    {path: 'slide/:state/:id', component: () => import('pages/SlidePage.vue')},
    {path: 'question/:id', component: () => import('pages/InteractiveVideo/PreviewPage.vue')},
    {path: 'prompts/:action/:id', component: () => import('pages/PromptsPage.vue?p=1')},
    {path: 'prompts/:id', component: () => import('pages/PromptsPage.vue?p=2')},
    {path: 'order/prompts/:id', component: () => import('pages/PromptsPage.vue?p=2')},
    {path: ':type/import/:mode', component: () => import('pages/com/ImportPage.vue')},
    {path: ':type/import/:mode/:id', component: () => import('pages/com/ImportPage.vue')},
    {path: ':type/import/:mode/:to/:group', component: () => import('pages/com/ImportPage.vue')},
    {path: ':type/import/0/:to/:group', component: () => import('pages/com/QuickSession.vue')},
    {path: ':type/import/:mode/:to/:group/:from', component: () => import('pages/com/ImportPage.vue')},
    {path: 'offer/:id', component: () => import('pages/booking/offerPage.vue')},

    // {path: 'package/:id', component: () => import('pages/packageDetailPage.vue')},
    {path: 'booked/:id', component: () => import('pages/BookedPage.vue')},
  ],
}
