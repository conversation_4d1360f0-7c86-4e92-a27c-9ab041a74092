export default {
  path: '/sys/',
  component: () => import('layouts/SysLayout.vue'),
  children: [
    {icon: 'home', text: 'Home', path: '', component: () => import('pages/sys/IndexPage.vue')},
    {icon: 'list', text: 'School', path: 'school', component: () => import('pages/sys/SchoolPage.vue')},
    {path: 'school/:id', component: () => import('pages/sys/SchoolEdit.vue')},
    {path: 'order/OrderList', icon: 'o_shopping_cart', text: 'Order/Product', component: () => import('pages/sys/OrderList.vue')},
    {path: 'order/MentorAcademic', component: () => import('pages/sys/MentorAcademic.vue')},
    {path: 'order/PremiumWorkshops', component: () => import('pages/sys/PremiumWorkshops.vue')},
    {path: 'commission-setting', icon: 'settings', text: 'Commission setting', component: () => import('pages/sys/CommissionSetting/Index.vue')},
    {path: 'bonus-setting', icon: 'list_alt', text: 'Bonus setting', component: () => import('pages/sys/BonusSetting/Index.vue')},
    {path: 'point-setting', icon: 'list_alt', text: 'Point setting', component: () => import('pages/sys/PointSetting/Index.vue')},
    {path: 'campus-location', icon: 'list_alt', text: 'On campus service locations', component: () => import('pages/sys/CampusLocation/Index.vue')},
    {path: 'my_poster', icon: 'list_alt', text: 'My Poster', component: () => import('pages/poster/Index.vue')},
    {path: 'followUpSales', icon: 'reduce_capacity', text: 'Follow-up sales', component: () => import('pages/sys/FollowUpSales.vue')},
    {icon: 'list', text: 'Managing personnel', path: 'manager/list', component: () => import('pages/sys/Manager/Index.vue')},
    {path: 'manager/add', component: () => import('pages/sys/Manager/Edit.vue')},
    {path: 'manager/edit/:id', component: () => import('pages/sys/Manager/Edit.vue')},
    {path: 'manager/roles', component: () => import('pages/sys/Manager/Roles.vue')},
    {path: 'manager/roles/:type', component: () => import('pages/sys/Manager/RolesDetail.vue')},
    // { icon: 'verified_user', text: 'Authentication', path: 'usercert', component: () => import('pages/sys/UserCertPage.vue')},
    // {path: 'usercert/:id', component: () => import('pages/sys/UserCertDetail.vue')},
    {icon: 'list', text: 'Published', path: 'published', component: () => import('pages/sys/PublishedPage.vue')},
    {icon: 'mail', text: 'MailTpl', path: 'mailTpl', component: () => import('pages/sys/MailTpl.vue')},

    {icon: 'notification_important', text: 'Notice', path: 'notice', component: () => import('pages/sys/dev/NoticePage.vue')},
    {path: 'posters', icon: 'wallpaper', text: 'Posters', component: () => import('pages/sys/PostersPage.vue')},
    // { icon: 'local_offer', text: 'Tags', path: 'tags', component: () => import('pages/sys/TagsList.vue')},
    // {path: 'tags/:id', component: () => import('pages/sys/TagsEdit.vue')},
    // { icon: 'local_offer', text: 'Tags old', path: 'tagsOld', ext: '?uid=1', component: () => import('pages/com/TagsPage.vue')},
    // { icon: 'style', text: 'Tags-PD', path: 'tags-pd', component: () => import('pages/sys/TagsPDPage.vue')},
    // { icon: 'gavel', text: 'Terms', path: 'terms', component: () => import('pages/sys/TermsPage.vue')},
    // { icon: 'feedback', text: 'Feedback', path: 'feedback', component: () => import('pages/sys/FeedbackPage.vue')},
    // { icon: 'social_distance', text: 'Promotes', path: 'promotes', component: () => import('pages/sys/PromotesPage.vue')},
    {icon: 'settings', text: 'Website', path: 'conf/website', component: () => import('pages/sys/ConfWebsite.vue')},

    // unit-plan/task templates
    {icon: 'settings', text: 'Templates', path: 'template', component: () => import('pages/sys/UnitPlanTemplateListPage.vue')},

    // academic setting
    {
      icon: 'settings',
      text: 'Academic Settings',
      path: 'academic-setting',
      component: () => import('pages/account/academic-setting/ListPage.vue'),
    },

    // teacher verification
    {
      icon: 'settings',
      text: 'Teacher Verification',
      path: 'teacher-verification',
      component: () => import('pages/teacher-verification/IndexPage.vue'),
    },
    {
      icon: 'settings',
      text: 'Premium course material',
      path: 'premium-content',
      component: () => import('pages/teacher-verification/PremiumContent/Index.vue'),
    },
    {
      icon: 'settings',
      text: 'On Campus Verification',
      path: 'campus-verification',
      component: () => import('pages/sys/CampusVerification/Index.vue'),
    },

    // teaching accident
    {
      icon: 'settings',
      text: 'Teaching Accident',
      path: 'teaching-accident',
      component: () => import('pages/teaching-accident/IndexPage.vue'),
    },

    // ambassador
    {
      icon: 'settings',
      text: 'Ambassador',
      path: 'ambassador',
      component: () => import('pages/ambassador/SysIndexPage.vue'),
    },

    // new prompt
    {
      icon: 'settings',
      text: 'New prompt',
      path: 'new-prompt',
      component: () => import('pages/new-prompt/IndexPage.vue'),
    },
    {
      icon: 'settings',
      text: 'Application procedure',
      path: 'application-procedure',
      component: () => import('pages/sys/ApplicationPro/ApplicationProcedure.vue'),
    },
    // agreement
    {
      icon: 'description',
      text: 'Agreement',
      path: 'agreement',
      component: () => import('pages/sys/agreement/IndexPage.vue'),
    },
    {path: 'agreement/detail/:type/:mode', component: () => import('pages/sys/agreement/OnePage.vue')},

    {
      icon: 'settings',
      text: 'Suspended list',
      path: 'teaching-accident/suspended-list',
    },

    {icon: 'package_2', text: 'Service Package', path: 'package', component: () => import('pages/sys/package/PackagePage.vue')},
    {path: 'package/edit', component: () => import('pages/sys/package/PackageEditPage.vue')},
    {path: 'package/editOri', component: () => import('pages/sys/package/PackageEditPage copy.vue')},
    {path: 'package/view', component: () => import('pages/sys/package/PackageViewPage.vue')},

    // for dev
    {icon: 'home', text: 'Home', path: 'dev/', component: () => import('pages/sys/IndexPage.vue')},
    {icon: 'list', text: 'Admin', path: 'dev/admin', component: () => import('pages/sys/dev/AdminPage.vue')},
    {icon: 'list', text: 'Teacher', path: 'teacher', component: () => import('pages/sys/dev/TeacherPage.vue')},
    {icon: 'desktop_mac', text: 'Sessions', path: 'dev/session', component: () => import('pages/sys/dev/SessionPage.vue')},
    {icon: 'mail', text: 'MailTpl', path: 'dev/mailTpl', component: () => import('pages/sys/dev/MailTpl.vue')},
    {icon: 'notification_important', text: 'Notice', path: 'dev/notice', component: () => import('pages/sys/dev/NoticePage.vue')},
    {icon: 'flag', text: 'CountryCodes', path: 'dev/countrycodes', component: () => import('pages/sys/dev/CountryCodes.vue')},
    {icon: 'warning_amber', text: 'Order Disputes', path: 'dev/order-disputes', component: () => import('pages/sys/OrderDisputes/Index.vue')},

    // for sys
    {meta: {role: 'sys'}, icon: 'people', text: 'Users', path: 'users', component: () => import('pages/sys/UsersPage.vue')},
    {meta: {role: 'sys'}, icon: 'perm_media', text: 'Files', path: 'files', component: () => import('pages/sys/FilesPage.vue')},
    {meta: {role: 'sys'}, icon: 'storage', text: 'Model', path: 'model', component: () => import('pages/sys/ModelList.vue')},
    {meta: {role: 'sys'}, icon: 'account_tree', text: 'Curriculum', path: 'curriculum', component: () => import('pages/sys/CurriculumPage.vue')},
    {meta: {role: 'sys'}, icon: 'article', text: 'Content', path: 'content', component: () => import('pages/sys/ContentPage.vue')},
    {meta: {role: 'sys'}, icon: 'meeting_room', text: 'Rooms', path: 'rooms', component: () => import('pages/sys/RoomsPage.vue')},
    {path: 'curriculum/subject/:id', component: () => import('pages/sys/CurriculumSubject.vue')},
    {meta: {role: 'sys'}, icon: 'bug_report', text: 'Test', path: 'test', component: () => import('pages/sys/SysTest.vue')},
    {meta: {role: 'sys'}, icon: 'code', text: 'code', path: 'code', component: () => import('pages/sys/CodePage.vue')},
    {meta: {role: 'sys'}, icon: 'tune', text: 'OldDict', path: 'conf/oldDict', component: () => import('pages/sys/ConfOldDict.vue')},
    {meta: {role: 'sys'}, icon: 'api', text: 'apiTest', path: 'apiTest', component: () => import('pages/sys/ApiTestForm.vue')},
    {meta: {role: 'sys'}, icon: 'mail', text: 'MailTplOld', path: 'mailTplOld', component: () => import('pages/sys/MailTplOld.vue')},
    {meta: {role: 'sys'}, icon: 'list', text: 'UnitOld', path: 'unit', component: () => import('pages/sys/UnitPage.vue')},
    {meta: {role: 'sys'}, icon: 'list', text: 'TaskOld', path: 'task', component: () => import('pages/sys/TaskPage.vue')},
    {meta: {role: 'sys'}, icon: 'list', text: 'SessionOld', path: 'sessionOld', component: () => import('pages/sys/SessionPageOld.vue')},
    {meta: {role: 'sys'}, icon: 'people', text: 'UsersSysOld', path: 'usersSys', component: () => import('pages/sys/UsersSys.vue')},
    {meta: {role: 'sys'}, icon: 'menu_book', text: ' 大纲 Old', path: 'target', component: () => import('pages/sys/TargetPage.vue')},
    {meta: {role: 'sys'}, icon: 'settings', text: 'Update', path: 'update', component: () => import('pages/sys/UpdatePage.vue')},
    {meta: {role: 'sys'}, icon: 'settings', text: 'Conf', path: 'conf', component: () => import('pages/sys/ConfPage.vue')},
    {meta: {role: 'sys'}, icon: 'settings', text: 'Conf User', path: 'conf-user', component: () => import('pages/sys/ConfUserPage.vue')},
    {meta: {role: 'sys'}, icon: 'settings', text: 'Conf subject', path: 'subject', component: () => import('pages/sys/SubjectPage.vue')},
    {meta: {role: 'sys'}, icon: 'mail', text: 'Mail', path: 'conf/mail', component: () => import('pages/sys/ConfMail.vue')},

    // { icon: 'setting', text: 'Setting', path: 'setting', component: () => import('pages/sys/SettingPage.vue') },
  ],
}
