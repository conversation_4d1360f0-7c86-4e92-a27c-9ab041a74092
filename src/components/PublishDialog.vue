<template>
  <q-dialog v-model="showPublishDialog" :maximized="$q.screen.lt.sm" @hide="onPublishDialogHide($event)" @show="onPublishDialogShow">
    <!--
    min-width: 80vw
    -->
    <q-card class="my-card">
      <q-toolbar class="lt-sm">
        <q-btn flat round dense icon="close" v-close-popup />
        <q-toolbar-title>Publish to library</q-toolbar-title>
      </q-toolbar>
      <template v-if="hasNonOriginal">
        <q-card-actions>
          <q-space></q-space>
          <q-btn flat round dense icon="close" v-close-popup />
        </q-card-actions>
        <q-card-section>
          <div v-if="nonOriginalLinks?.length">
            <div class="text-subtitle1">There are unoriginal Tasks/Service sessions existing in the link content of the Unit/Service module.</div>
            <div class="text-subtitle2 q-pb-md">list as bellow:</div>
            <ol>
              <li v-for="(item, key) in nonOriginalLinks" :key="key">
                {{ item.name }}
              </li>
            </ol>
          </div>
          <div v-if="subNonOriginalLinks?.length">
            <div class="text-subtitle1 q-pb-md">
              There are unoriginal contents existing in Preparations or After class sections that are linked with Task/ Service session.
            </div>
            <ol>
              <li v-for="(item, key) in subNonOriginalLinks" :key="key">
                {{ item.name }}
              </li>
            </ol>
          </div>
        </q-card-section>
        <q-card-section>
          <div class="text-subtitle1">Please republish your contents after you have updated.</div>
        </q-card-section>
      </template>
      <q-form @submit.prevent.stop="onSubmit" v-else>
        <div style="max-height: 80vh" class="scroll">
          <q-card-section>
            <q-input
              outlined
              v-model="price"
              mask="#.##"
              fill-mask="0"
              reverse-fill-mask
              @update:model-value="onPriceUpdate"
              label="Price"
              placeholder="Enter amount">
              <template v-slot:prepend>
                <q-icon name="attach_money" size="lg" />
              </template>
            </q-input>
          </q-card-section>
          <q-card-section class="q-py-none">
            <q-list>
              <q-item class="q-px-none">
                <q-item-section avatar>
                  <q-toggle :disable="!parseFloat(price)" color="primary" @update:model-value="onDiscountToggled" v-model="discount" val="battery" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Discount</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
          <template v-if="discount">
            <q-card-section>
              <q-input
                ref="percentageRef"
                lazy-rules
                :rules="percentageRules"
                outlined
                v-model="percentage"
                mask="#"
                reverse-fill-mask
                label="Percentage off"
                placeholder="Please insert percentage off">
                <template v-slot:append>
                  <q-icon name="percent" size="xs" />
                </template>
              </q-input>
              <div class="items-center">
                <div>Discount ends on</div>
                <input id="flat-duration" class="full-width date-input" placeholder="Discount ends on" v-model="discountEnd" />
              </div>
            </q-card-section>
            <q-card-section class="hidden">
              <q-input
                outlined
                v-model="discountSize"
                mask="#"
                fill-mask="0"
                reverse-fill-mask
                label="Group discount size"
                placeholder="Enter the number of group buyers">
              </q-input>
            </q-card-section>
          </template>
          <template v-if="!props.edit && !attributes.service && props.premium">
            <q-separator inset />
            <q-card-section ref="scrollTargetRef">
              <q-list>
                <q-item class="q-px-none">
                  <q-item-section avatar>
                    <q-toggle :disable="selfStudyLinks.length == 0" color="primary" v-model="studyCenter" @update:model-value="onStudentCenterUpdate" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>Turn on to publish it to student study center</q-item-label>
                    <q-item-label caption v-if="one.mode == 'unit'"
                      >There are {{ selfStudyLinks.length }} self-study session{{ selfStudyLinks.length > 1 ? 's' : '' }} under this unit,</q-item-label
                    >
                    <q-item-label caption v-else-if="selfStudyLinks.length">This task is suitable for self-study,</q-item-label>
                    <q-item-label caption>Turn on to publish it to student study center where global students could buy to study the session.</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
            <q-card-section class="text-primary text-weight-medium" v-if="selfStudyLinks.length">
              Please complete the Bloom’s Taxonomy
              <q-btn href="https://youtu.be/ayefSTAnCR8" target="_blank" round dense flat size="sm" icon="o_contact_support" color="grey-7"> </q-btn>
              of learning objectives before publishing to public study center
            </q-card-section>
          </template>
          <div v-if="studyCenter">
            <q-card-section v-for="(link, index) in selfStudyLinks" :key="index">
              <div class="text-h5 q-mt-sm q-mb-md">{{ link.name }}</div>
              <div class="rounded-borders q-pa-md border-1">
                <UnitView publish :id="link._id" @loaded="onUnitViewLoaded"></UnitView>
              </div>
            </q-card-section>
          </div>
        </div>
        <q-separator inset />
        <q-card-actions align="right">
          <q-btn class="q-mr-md" outline v-close-popup rounded no-caps>
            <div class="q-px-sm">
              <q-icon name="close"></q-icon>
              Cancel
            </div>
          </q-btn>
          <q-btn color="primary" type="submit" :loading="loading" rounded no-caps>
            <div class="q-px-sm">
              <q-icon :name="edit ? 'svguse:/v2/icons.svg#publish' : 'library_books'"></q-icon>
              {{ edit ? 'Update' : 'Publish to library' }}
            </div>
          </q-btn>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, watch, onMounted, inject, computed} from 'vue'
import {useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {date} from 'quasar'
import UnitView from 'components/UnitView.vue'
import useUnit from 'src/composables/account/unit/useUnit.js'
const router = useRouter()

const pub = pubStore()
const {relateLinkList, allRelateLinkList, publish} = useUnit()

const showPublishDialog = ref(false)
const price = ref(null)
const discount = ref(false)
const studyCenter = ref(false)
const percentage = ref(null)
const discountSize = ref(null)
const discountEnd = ref(null)
const percentageRef = ref(null)
const changed = ref(false)
const submitted = ref(false)
const contentsType = inject('ContentsType')
const nonOriginalLinks = ref([])
const subNonOriginalLinks = ref([])

const percentageRules = [(val) => (val && val > 0 && val < 100) || 'The percentage must be greater than 0 and less than 100.']
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  edit: {
    type: Boolean,
    default: false,
  },
  premium: {
    type: Boolean,
    default: false,
  },
  count: {
    type: Number,
    default: 0,
  },
  one: {
    type: Object,
    default: () => {
      return {}
    },
  },
})

const emit = defineEmits(['hide', 'show'])

/*
  computeds
*/
const hasNonOriginal = computed(() => {
  return nonOriginalLinks.value?.length || subNonOriginalLinks.value?.length
})

const attributes = computed(() => {
  return contentsType[props.one.mode]
})

watch(
  () => props.show,
  (first) => {
    if (first) {
      initDialog()
    }
  }
)

const scrollTargetRef = ref(null)
const onStudentCenterUpdate = () => {
  setTimeout(() => {
    //scrollTargetRef.value.$el.scrollIntoView({behavior: 'smooth'})
  }, 1000)
}

const idsWithNullBloom = ref([])
const onUnitViewLoaded = async (val) => {
  if (val?.length) {
    idsWithNullBloom.value = val
    await sleep(200)
    //document.getElementById(val[0])?.scrollIntoView({behavior: 'smooth'})
  }
}

const checkBloom = () => {
  return true
  let allFilled = true
  console.log(idsWithNullBloom.value, 123)
  idsWithNullBloom.value.forEach((item) => {
    const doc = document.getElementById(item)
    if (!doc?.children?.[1]?.value && allFilled) {
      console.log(doc.children, 123)

      allFilled = false
      document.getElementById(item)?.scrollIntoView({behavior: 'smooth'})
    }
  })

  return allFilled
}

const onPriceUpdate = (val) => {
  if (parseFloat(val) == 0) {
    discount.value = false
  }
}

let flatDurationIns = null
const initFlatDuration = () => {
  if (flatDurationIns) {
    try {
      flatDurationIns.destroy()
    } catch (e) {
      console.log(e)
    }
    flatDurationIns = null
  }

  const config = {
    enableTime: true,
    time_24hr: true,
    minuteIncrement: 1,
    dateFormat: 'Y-m-d H:i',
    minDate: new Date(),
    /*
    defaultDate: form.value.regDate,
    maxDate: allLiveStart.value,
    maxDate: ??
    disable: [
      function (date) {
        return new Date(date) > new Date(end.value)
      },
    ],
    */
  }

  flatDurationIns = flatpickr('#flat-duration', config)
}

const onDiscountToggled = async (val) => {
  if (val) {
    await sleep(200)
    initFlatDuration()
  }
}

const initDialog = async () => {
  changed.value = false
  if (props.one.discount?.val) {
    discount.value = true
    percentage.value = props.one.discount.val
    discountSize.value = props.one.discount.size
  }
  if (props.one.discount?.price) {
    price.value = (props.one.discount.price / 100).toFixed(2)
  }
  if (props.one.discount?.end) {
    discountEnd.value = date.formatDate(props.one.discount.end, 'YYYY-MM-DD HH:mm')
  }
  if (!props.edit) {
    await getSelfStudyLinks()
    await getSubLinksOfAll()
  }
  showPublishDialog.value = true
  if (selfStudyLinks.value.length && props.premium) {
    studyCenter.value = true
  }
  await sleep(200)
  initFlatDuration()
}

const selfStudyLinks = ref([])
const getSelfStudyLinks = async () => {
  //for task or unit mode ONLY
  selfStudyLinks.value = []
  //if (['task', 'pdTask'].includes(props.one.mode) && props.one.sessionType == 'student') {
  if (['task', 'video'].includes(props.one.mode) && props.one.sessionType == 'student') {
    selfStudyLinks.value.push(props.one)
  }

  //if (['unit', 'pdUnit'].includes(props.one.mode) || (['task', 'pdTask'].includes(props.one.mode) && props.one.sessionType == 'live')) {
  const rs = await relateLinkList({rid: props.one._id})
  //#4734,#604
  nonOriginalLinks.value = rs.filter((e) => e.sourceUid && e.sourceUid !== pub.user._id)
  if (['unit'].includes(props.one.mode) || (['task'].includes(props.one.mode) && props.one.sessionType == 'live')) {
    if (rs) {
      rs.forEach((item) => {
        if (['task', 'pdTask', 'video'].includes(item.mode) && item.sessionType == 'student') {
          selfStudyLinks.value.push(item)
        }
      })
    }
  }
}

const allSublinks = ref([])
const getSubLinksOfAll = async () => {
  /*
  const selfStudyLinkIds = []
  selfStudyLinks.value.forEach((item) => {
    selfStudyLinkIds.push(item._id)
  })
  const rs = await allRelateLinkList({rid: selfStudyLinkIds})
  */

  const rs = await allRelateLinkList({rid: props.one.link?.map((e) => e.id)})

  selfStudyLinks.value.forEach((item) => {
    if (rs[item._id]?.length) {
      const links = rs[item._id]
      item.link.forEach((_item) => {
        const _link = links.find((__item) => __item._id == _item.id)
        allSublinks.value.push({..._link, ...{group: item.group, parent: item._id}})
      })
    }
  })

  if (rs) {
    subNonOriginalLinks.value = [].concat(...Object.values(rs)).filter((e) => e.sourceUid && e.sourceUid !== pub.user._id)
  }
}

const getSubLinksOf = (link) => {
  return allSublinks.value.filter((item) => item.parent == link._id)
}

const onPublishDialogHide = () => {
  submitted.value = false
  price.value = null
  percentage.value = null
  discountSize.value = null
  discountEnd.value = null
  discount.value = false
  emit('hide', changed.value)
  changed.value = true
}

const onPublishDialogShow = () => {
  emit('show')
}

const loading = ref(false)
async function publishFn(query) {
  //const rs = await publish({...query, ...{'publish.lib': true}})
  const rs = await publish(query)
  if (rs.code === 400) return googleAuthExt().then(() => publishFn(query))
}
const onSubmit = async () => {
  submitted.value = true
  if (discount.value) {
    percentageRef.value.validate()
  }
  if (studyCenter.value) {
    if (!checkBloom()) return console.log('bloom check faild')
  }
  if (discount.value && percentageRef.value.hasError) return console.log('percentageRef error:', percentageRef.value.hasError)

  let count = 1
  let doneCount = 0
  if (!props.edit) {
    //count += props.one.link.length
  }

  if (studyCenter.value) {
    count += selfStudyLinks.value.length
    if (allSublinks.value.length) {
      count += allSublinks.value.length
    }
  }

  const query = {_id: props.one._id, discount: {price: parseFloat(price.value * 100)}}
  if (discount.value) {
    query.discount.val = parseInt(percentage.value)
    if (discountEnd.value) {
      query.discount.end = new Date(discountEnd.value).toISOString()
    }
  } else {
    query.discount.val = 0
    query.discount.end = null
  }
  const dialog = $q.dialog({
    message: `${props.edit ? 'Updating...' : 'Publishing... 0%'}`,
    progress: true,
    persistent: true,
    ok: false,
  })
  await publishFn(query)
  doneCount += 1
  if (!props.edit) {
    dialog.update({
      message: `Publishing... ${Math.ceil((100 * doneCount) / count)}%`,
    })

    /*
      //https://github.com/zran-nz/bug/issues/4548#issuecomment-2540467667
      for (let i = 0; i < props.one.link.length; i++) {
        const link = props.one.link[i]
        const rs = await publish({_id: link.id, 'publish.link': true})
        doneCount += 1
        dialog.update({
          message: `Publishing... ${Math.ceil((100 * doneCount) / count)}%`,
        })
      }*/
  }

  if (studyCenter.value) {
    for (let i = 0; i < selfStudyLinks.value.length; i++) {
      const link = selfStudyLinks.value[i]
      const subLinks = getSubLinksOf(link)
      const childs = []
      for (let j = 0; j < subLinks.length; j++) {
        const subLink = subLinks[j]
        const obj = await createSessionForTool(subLink)
        childs.push(obj)
        doneCount += 1
        dialog.update({
          message: `Publishing... ${Math.ceil((100 * doneCount) / count)}%`,
        })
      }

      const rs = await App.service('session').create({
        type: 'selfStudy',
        status: 'student',
        name: link.name,
        sessionType: 'student',
        id: link.sid,
        childs,
        subjects: getUniqueSubjects(link),
        cid: link._id,
        image: link.cover,
        unitType: link.type,
        regMax: 0,
      })
      doneCount += 1
      dialog.update({
        message: `Publishing... ${Math.ceil((100 * doneCount) / count)}%`,
      })

      for (let i = 0; i < childs.length; i++) {
        await App.service('session').patch(childs[i]['_id'], {pid: rs._id})
      }
    }
  }

  if (props.edit) {
    $q.notify({type: 'info', message: 'Update successfully'})
  } else {
    $q.notify({type: 'info', message: 'Publish successfully'})
    router.replace('/home/<USER>')
  }

  changed.value = true
  showPublishDialog.value = false
  dialog.hide()
}

const getUniqueSubjects = (item) => {
  const allSubjects = []
  item.subjects?.forEach((s) => {
    allSubjects.push({label: s.label, value: s.value})
  })
  item.outlineSubjects?.forEach((value) => {
    allSubjects.push({label: value, value})
  })
  const uniqueSubjects = allSubjects.reduce((unique, item) => {
    const found = unique.find((obj) => obj.value === item.value)
    if (!found) {
      unique.push(item)
    }
    return unique
  }, [])

  return uniqueSubjects
}

const createSessionForTool = async (link) => {
  const {_id, sid, cid} = await App.service('session').create({
    //type: 'taskWorkshop',
    type: 'tool',
    status: 'student',
    name: link.name,
    sessionType: 'student',
    id: link.sid,
    cid: link._id,
    image: link.cover,
    unitType: link.type,
    regMax: 0,
  })

  return {
    _id,
    sid,
    cid,
    mode: link.mode,
    group: link.group,
  }
}

onMounted(async () => {
  Aincludes('/v2/plugin/flatpickr.min.css', 'css')
  await Aincludes('/v2/plugin/flatpickr.min.js', 'js')
  await sleep(500)
})
</script>
<style scoped lang="sass">
.my-card
  width: 700px
.border-1
  border: 1px solid $grey-4
.date-input
  border: 1px solid #ccc
  border-radius: 3px
  padding: 0.9rem
  background: transparent
  &:focus
    border-color: $primary
</style>
