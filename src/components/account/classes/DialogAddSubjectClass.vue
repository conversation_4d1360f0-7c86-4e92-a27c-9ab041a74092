<template>
  <div>
    <q-btn
      v-if="!isEditing"
      class="bg-teal-4 text-white"
      size="md"
      no-caps
      flat
      unelevated
      rounded
      label="Add new"
      icon="add_circle_outline"
      @click="onAddClick" />
    <q-btn v-if="isEditing" class="text-gray" size="md" dense icon="o_edit" flat unelevated rounded @click="onEditClick" />

    <q-dialog v-model="isDialogShow" persistent>
      <div class="bg-white rounded-lg" style="width: clamp(320px, 60%, 600px)">
        <div class="text-h6 q-pa-md">Subject class</div>
        <ErrorBanner v-model="isErrorShow" message="Please fill in all required fields before saving." />
        <!-- <pre>{{ formValidMap }}</pre> -->
        <div class="q-pa-md">
          <div>
            <div class="text-subtitle1 q-my-sm" :class="[formValidMap.name ? '' : 'text-red']">* Class name</div>
            <q-input type="text" outlined v-model="form.name" placeholder="Enter class name" :class="[formValidMap.name ? '' : 'input-warning']" />

            <div class="text-subtitle1 q-my-sm">* Term</div>
            <q-select v-model="form.term" :options="termOptions" label="Select term" map-options emit-value outlined class="q-mb-sm" behavior="menu" />

            <div class="text-subtitle1 q-my-sm" :class="[formValidMap.curriculum ? '' : 'text-red']">* Curriculum</div>
            <div class="flex items-center">
              <q-btn
                v-for="curriculum in currentCurriculumList.filter((e) => e?.value !== 'pd')"
                :key="curriculum._id"
                flat
                no-caps
                dense
                :label="curriculum?.label || ''"
                class="q-px-sm q-mr-sm q-mt-sm"
                :class="[form.curriculum === curriculum._id ? 'bg-green text-white' : '']"
                style="border: 1px solid"
                :disable="false"
                @click="() => onCurriculumClick(curriculum)" />
            </div>
            <div class="text-subtitle1 q-my-sm" :class="[formValidMap.subject ? '' : 'text-red']">* Subject</div>
            <div class="flex items-center">
              <q-toggle v-model="form.isTemporarySubject" />
              <div>Add temporary subject</div>
            </div>
            <div v-if="form.isTemporarySubject">
              <q-btn v-if="!form.subjectTemporary" flat icon="add" no-caps color="teal" label="Add subject">
                <!-- form.value.subjectTemporary -->
                <q-popup-edit v-model="form.subjectTemporary" auto-save v-slot="scope">
                  <q-input v-model="scope.value" dense autofocus @keyup.enter="scope.set" />
                </q-popup-edit>
              </q-btn>

              <div v-if="form.subjectTemporary" class="flex items-center">
                <div class="item-wrapper">
                  <q-chip flat square no-caps :label="form.subjectTemporary" class="q-px-sm bg-green text-white" style="border: 1px solid" />
                  <q-btn rounded flat no-caps icon="close" size="sm" dense class="item-delete-button text-black bg-red-3" @click="onDeleteTemporarySubject" />
                </div>
              </div>
            </div>

            <div v-else>
              <!-- <pre>{{ currentSubjectList?.[0] }}</pre> -->
              <!-- <pre>{{ subjectList?.[0] }}</pre> -->
              <div v-if="currentSubjectList?.length" class="flex items-center">
                <q-btn
                  v-for="subject in currentSubjectList"
                  :key="subject._id"
                  flat
                  no-caps
                  dense
                  :label="subject?.name || ''"
                  class="q-px-sm q-mr-sm q-mt-sm"
                  :class="[form.subject === subject._id ? 'bg-green text-white' : '']"
                  style="border: 1px solid"
                  :disable="false"
                  @click="() => onSubjectClick(subject)" />
              </div>
              <NoData v-else size="8rem" message="No subject" />
            </div>

            <div class="text-subtitle1 q-my-sm" :class="[formValidMap.block ? '' : 'text-red']">* Teaching period and frequency</div>
            <div class="flex items-center">
              <div>School blocks</div>
              <q-toggle :modelValue="form.isCustomizeBlock" @update:modelValue="(bool) => onSetInCustomizeBlock(bool)" />
              <div>Customize</div>
            </div>
            <div class="q-my-md">
              <q-btn v-if="form.block?.length" rounded flat no-caps label="View blocks" class="text-teal" style="border: 1px solid" @click="onViewBlockClick" />
              <q-btn
                v-else
                rounded
                flat
                no-caps
                label="Set blocks"
                :class="[formValidMap.block ? 'text-teal' : 'text-red']"
                style="border: 1px solid"
                @click="onSetBlockClick" />
            </div>

            <div class="flex items-center">
              <div>Repeat every</div>
              <q-select v-model="form.every" :options="[1, 2, 3, 4]" outlined dense class="q-mx-sm" />
              <div>Week</div>
            </div>

            <!-- <div class="rounded-md border-grey q-pa-md q-my-md"> -->
            <!--   <div class="flex items-center"> -->
            <!--     <div>Student self-enroll</div> -->
            <!--     <q-toggle v-model="form.enroll.enable" /> -->
            <!--   </div> -->
            <!--   <NoData /> -->
            <!--   <div>Choose the students who can self-enroll</div> -->
            <!--   <div class="flex items-center"> -->
            <!--     <div>Max participants</div> -->
            <!--     <q-toggle v-model="form.selfEnroll" /> -->
            <!--   </div> -->
            <!--   <q-input type="number" v-model="form.maxParticipants" /> -->
            <!-- </div> -->
            <!---->
            <!-- <div class="rounded-md border-grey q-pa-md q-my-md"> -->
            <!--   <div class="flex items-center"> -->
            <!--     <div>Approval required</div> -->
            <!--     <q-toggle v-model="form.approval" /> -->
            <!--   </div> -->
            <!--   <div v-if="form.approval"> -->
            <!--     <div>Question for student to answer upon application</div> -->
            <!--     <div>Why why why</div> -->
            <!--     <q-btn rounded unelevate no-caps label="Add question" icon="add" class="" @click="() => {}" /> -->
            <!--   </div> -->
            <!-- </div> -->
          </div>
          <div class="flex justify-end q-gutter-md q-mt-md">
            <q-btn rounded flat no-caps label="Go back" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="closeDialog" />
            <q-btn rounded flat no-caps label="Confirm" icon="done" class="bg-teal-4 text-white" @click="onAddClass" />
          </div>
        </div>
      </div>
    </q-dialog>

    <q-dialog v-model="isSetBlockDialogShow" persistent>
      <div class="bg-white rounded-lg q-pa-md" style="width: clamp(360px, 70%, 720px); max-width: 720px">
        <CalendarWeekHoursNoTool
          v-if="isSetBlockCalendarShow"
          v-model="weekData"
          :editable="form.isCustomizeBlock"
          :clickable="true"
          @update:modelValue="changeBlock" />
        <div class="flex justify-end q-gutter-md q-mt-md">
          <q-btn rounded flat no-caps label="Go back" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="() => onCloseSetBlock(true)" />
          <q-btn rounded flat no-caps label="Confirm" icon="done" class="bg-teal-4 text-white" @click="() => onCloseSetBlock()" />
        </div>
      </div>
    </q-dialog>

    <q-dialog v-model="isViewBlockDialogShow" persistent>
      <div class="bg-white rounded-lg q-pa-md" style="width: clamp(360px, 70%, 720px); max-width: 720px">
        <CalendarWeekHoursNoTool v-if="isViewBlockCalendarShow" v-model="form.block" :editable="false" :clickable="false" />
        <div class="flex justify-end q-gutter-md q-mt-md">
          <q-btn rounded flat no-caps label="Go back" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="onCloseViewBlock()" />
        </div>
      </div>
    </q-dialog>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useQuasar} from 'quasar'

import useSchool from 'src/composables/common/useSchool'
import useSchoolYear from 'src/composables/account/school/useSchoolYear'
import useSchoolTerm from 'src/composables/account/school/useSchoolTerm'
import useClasses from 'src/composables/account/school/useClasses'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import useSubject from 'src/composables/account/academic/useSubject'

import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'
import OkDialog from 'src/components/utils/dialogs/OkDialog.vue'
import ErrorBanner from 'src/components/utils/ErrorBanner.vue'
import CalendarWeekHoursNoTool from 'components/CalendarWeekHoursNoTool.vue'

const $q = useQuasar()
const {currentCurriculumList} = useAcademicSetting()
const {schoolIdOrUserId, userId} = useSchool()
const {list: yearList} = useSchoolYear()
const {list: termList, getPlanList} = useSchoolTerm()
const {patchOneById, createOne} = useClasses()
const {list: subjectList} = useSubject()
const form = ref(null)

const props = defineProps({
  item: {
    type: Object,
    requite: true,
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
  editingData: {
    type: Object,
  },
})

const emit = defineEmits(['refetch'])

const yearMap = computed(() =>
  yearList.value.reduce((acc, cur) => {
    acc[cur._id] = cur
    return acc
  }, {})
)

const termOptions = computed(() =>
  termList.value.map((e) => {
    return {value: e._id, label: `${yearMap.value[e.year]?.title} - ${e.title}`}
  })
)

const isDialogShow = ref(false)

const hasBlock = ref(false)
async function onAddClick() {
  $q.loading.show()
  weekData.value = await setWeekData()
  if (!weekData.value?.length) {
    hasBlock.value = false
    form.value.isCustomizeBlock = true
  } else {
    hasBlock.value = true
    form.value.isCustomizeBlock = false
  }
  isDialogShow.value = true
  $q.loading.hide()
}
function closeDialog() {
  isDialogShow.value = false
  isErrorShow.value = false
  initFormValidMap()
  if (props.isEditing) {
    initForm(true)
  } else {
    initForm()
  }
}
function onEditClick() {
  console.log(props.editingData)
  isDialogShow.value = true
}

onMounted(() => {
  form.value = initForm(true)
})

function initForm(isInit = false) {
  const dto = {
    name: '',
    approval: true,
    curriculum: '',
    subject: '',
    subjectTemporary: '',
    term: props.item._id,

    enroll: {
      enable: false,
      classes: [],
    },
    // attachmentsCover: {
    //   filename: '',
    //   mime: '',
    //   hash: '',
    // },
    // deadline: '',
    maxParticipants: 0,
    // approvalRequired: false,
    // questions: [''],
    // block: [
    //   {
    //     every: 1,
    //     week: 0,
    //     repeat: 'week',
    //     start: '00:00',
    //     end: '00:00',
    //   },
    // ],
    isTemporarySubject: false,
    isCustomizeBlock: false,
    block: [],
    every: 1,
    repeat: 'week',
  }
  if (isInit) {
    if (props.isEditing && props.editingData) {
      return {...dto, ...props.editingData}
    } else {
      return dto
    }
  } else {
    form.value = dto
  }
}

// ref: https://github.com/zran-nz/doc/blob/master/fio/school.md

const currentSubjectList = computed(() => {
  const curriculumId = form.value.curriculum
  const target = currentCurriculumList.value.find((e) => e._id === curriculumId)
  if (target) {
    return subjectList.value.filter((e) => e?.curriculum?.includes(target.value))
  }
  return []
})

function onDeleteTemporarySubject() {
  form.value.subjectTemporary = ''
}
function onCurriculumClick(currciulum) {
  form.value.curriculum = currciulum?._id
  // form.value.curriculum = currciulum?.value
}
function onSubjectClick(subject) {
  form.value.subject = subject?._id
}

function onSetInCustomizeBlock(bool) {
  if (!bool && !hasBlock.value) {
    const title = ''
    const message = 'Your school has no set  teaching blocks, please contact school admin'
    const okButtonLabel = 'I got it'
    $q.dialog({
      component: OkDialog,
      componentProps: {title, message, okButtonLabel},
    })
      .onOk(() => {})
      .onCancel(() => {})
      .onDismiss(() => {})
    return
  }
  if (form.value.block?.length) {
    const title = ''
    const message = 'Switching data source mode will result in removing  current setting. Are you sure to continue?'
    const okButtonLabel = 'Yes'
    $q.dialog({
      component: ConfirmDialog,
      componentProps: {title, message, okButtonLabel},
    })
      .onOk(() => {
        form.value.block = []
        form.value.isCustomizeBlock = bool
      })
      .onCancel(() => {})
      .onDismiss(() => {})
    return
  }
  form.value.isCustomizeBlock = bool
}

const plans = ref([])
const weekData = ref([])
async function setWeekData() {
  if (form.value.isCustomizeBlock) return []
  const planRes = await getPlanList({schoolTerm: props.item._id})
  plans.value = planRes?.data || []
  if (!plans.value?.length) return []
  const list = []
  plans.value.forEach((e) => {
    e.week.forEach((week) => {
      e.block.forEach((block) => {
        const dto = {week: +week, start: block.start, end: block.end}
        list.push(dto)
      })
    })
  })
  return list
}

const isSetBlockDialogShow = ref(false)
const isSetBlockCalendarShow = ref(false)
async function onSetBlockClick() {
  $q.loading.show()
  weekData.value = await setWeekData()
  isSetBlockDialogShow.value = true
  setTimeout(() => {
    isSetBlockCalendarShow.value = true
  }, 300)
  $q.loading.hide()
}
function onCloseSetBlock(isReset = false) {
  if (isReset) {
    form.value.block = []
  }
  isSetBlockDialogShow.value = false
  isSetBlockCalendarShow.value = false
}

const isViewBlockDialogShow = ref(false)
const isViewBlockCalendarShow = ref(false)
async function onViewBlockClick() {
  $q.loading.show()
  weekData.value = await setWeekData()
  isViewBlockDialogShow.value = true
  setTimeout(() => {
    isViewBlockCalendarShow.value = true
  }, 300)
  $q.loading.hide()
}
function onCloseViewBlock() {
  isViewBlockDialogShow.value = false
  isViewBlockDialogShow.value = false
}

function changeBlock(arr) {
  console.warn('change', arr)
  if (form.value.isCustomizeBlock) {
    form.value.block = arr
  } else {
    const selected = arr.filter((e) => e?.select)
    form.value.block = selected
  }
}

const isErrorShow = ref(false)
const formValidMap = ref(initFormValidMap(true))
function initFormValidMap(isInit = false) {
  const dto = {
    name: true,
    curriculum: true,
    subject: true,
    block: true,
  }
  if (isInit) {
    return dto
  } else {
    formValidMap.value = dto
  }
}
function checkFormValid(dto) {
  let valid = true
  if (!dto?.name) {
    formValidMap.value.name = false
    valid = false
  } else {
    formValidMap.value.name = true
  }
  if (!dto?.curriculum) {
    formValidMap.value.curriculum = false
    valid = false
  } else {
    formValidMap.value.curriculum = true
  }
  if (!dto?.subject && !(dto?.isTemporarySubject && dto?.subjectTemporary)) {
    formValidMap.value.subject = false
    valid = false
  } else {
    formValidMap.value.subject = true
  }
  if (!dto?.block?.length) {
    formValidMap.value.block = false
    valid = false
  } else {
    formValidMap.value.block = true
  }
  return valid
}

const errorTimer = ref(null)

async function onAddClass() {
  // console.log(props.item)
  // console.log(form.value)
  const dto = {
    school: schoolIdOrUserId.value,
    name: form.value.name,
    // grade: item._id,
    host: userId.value,
    term: form.value.term,
    type: 'subject',
    curriculum: form.value.curriculum,
    block: form.value.block,
    every: form.value.every,
    repeat: form.value.repeat,
    isTemporarySubject: form.value.isTemporarySubject,
    isCustomizeBlock: form.value.isCustomizeBlock,
  }
  if (form.value.subject) {
    dto.subject = form.value.subject
  } else if (form.value.subjectTemporary) {
    dto.subjectTemporary = form.value.subjectTemporary
  }
  if (!checkFormValid(dto)) {
    isErrorShow.value = true
    errorTimer.value = setTimeout(() => {
      if (errorTimer.value) clearTimeout(errorTimer.value)
      isErrorShow.value = false
    }, 3000)
    return
  } else {
    isErrorShow.value = false
  }
  try {
    $q.loading.show()
    if (props.isEditing) {
      await patchOneById(props.editingData._id, dto)
    } else {
      await createOne(dto)
    }
    closeDialog()
    $q.notify({type: 'positive', message: 'Class added successfully'})
    emit('refetch')
    // await initAllData()
  } catch (error) {
    $q.notify({type: 'negative', message: 'Class added unsuccessfully'})
    console.error(error)
  } finally {
    $q.loading.hide()
  }
}
</script>

<style lang="scss" scoped>
.border-grey {
  border: 1px solid #aaa;
}
.item-wrapper {
  position: relative;
  &:hover {
    .item-delete-button {
      opacity: 1;
    }
  }
  .item-delete-button {
    position: absolute;
    top: -0.35rem;
    right: -0.65rem;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    cursor: pointer;
  }
}
.input-warning {
  outline: 1px solid red;
  border-radius: 0.25rem;
}
</style>
