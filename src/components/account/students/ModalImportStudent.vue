<template>
  <q-dialog v-model="isModalVisible" ref="dialogRef" @hide="onHide" class="dialog">
    <q-card>
      <q-card-section>
        <q-select
          outlined
          v-model="activeGrade"
          :options="gradeList"
          label="Select grade"
          color="primary"
          @update:model-value="onSelectGrade"
          options-selected-class="text-primary">
        </q-select>
      </q-card-section>
      <q-card-actions class="justify-center items-start">
        <div class="full-width q-gutter-md" style="max-height: 20rem; overflow: auto">
          <SubjectClassImportBtn
            v-for="item in classList"
            :key="item._id"
            :name="item.name"
            @click="onSelectClass(item)"
            :type="activeClass === item._id ? 'active' : studentDict[item._id]?.some((e) => e.joined) ? 'checked' : 'normal'"></SubjectClassImportBtn>
        </div>
        <q-item class="full-width justify-between" clickable dense v-ripple tag="label" v-if="!isEmpty(studentList)">
          <q-item-section avatar> </q-item-section>
          <q-item-section side style="padding-right: 0px">
            <q-checkbox v-model="checkedAll" left-label label="All" @update:model-value="onCheckedAll" />
          </q-item-section>
        </q-item>
        <q-virtual-scroll class="q-mt-md q-mb-md fit" :items="studentList" v-slot="{item, index}">
          <q-item clickable dense v-ripple tag="label" :key="index">
            <q-item-section avatar>
              <q-avatar class="" size="28px">
                <img :src="item.avatar || '/v2/img/avatar.png'" />
              </q-avatar>
            </q-item-section>
            <q-item-section class="items-center">
              <div class="text-left full-width">{{ item.name.join(' ') }}</div>
            </q-item-section>
            <q-item-section side>
              <q-checkbox v-model="item.checked" :disable="item.joined" :color="item.joined ? 'grey' : ''" @update:model-value="onChecked" />
            </q-item-section>
          </q-item>
        </q-virtual-scroll>
        <template v-if="isEmpty(studentList)">
          <NoData v-if="!loading" />
          <div v-else class="text-center q-pa-xl text-grey">
            <q-spinner-ball color="primary" size="2em" class="full-width" />
          </div>
        </template>

        <div class="flex justify-end full-width">
          <q-btn rounded outline class="self-end q-ma-sm" color="primary" label="Close" @click="onHide" />
          <q-btn
            rounded
            class="self-end q-ma-sm"
            color="primary"
            :disable="studentList.filter((item) => item.checked && !item.joined).length === 0"
            label="Confirm"
            icon="done"
            @click="onSubmit" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, watchEffect, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import nameFormatter from 'src/utils/formatters/nameFormatter'
import useGrade from 'src/composables/account/academic/useGrade'
import SubjectClassImportBtn from './SubjectClassImportBtn.vue'
import {pubStore} from 'stores/pub'
import useSchool from 'src/composables/common/useSchool'

const {schoolId, isAdmin} = useSchool()
const emit = defineEmits([...useDialogPluginComponent.emits, 'save'])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()
const {list: gradeList} = useGrade()
const classList = ref([])
const studentList = ref([])
const studentDict = ref({})
const activeGrade = ref()
const activeClass = ref()
const checkedAll = ref(false)
const pub = pubStore()

const props = defineProps({
  isVisible: {
    type: Boolean,
  },
  setIsVisible: {
    type: Function,
  },
  subjectClass: {
    type: String,
  },
})

const onHide = (e) => {
  isModalVisible.value = false
  activeGrade.value = null
  activeClass.value = null
  checkedAll.value = false
  classList.value = []
  studentList.value = []
}

const isModalVisible = computed({
  get() {
    return props.isVisible
  },
  set(newValue) {
    props.setIsVisible(newValue)
  },
})

const onSelectGrade = async () => {
  activeClass.value = null
  checkedAll.value = false
  studentList.value = []
  getClasses()
}

const loading = ref(false)
const getClasses = async () => {
  loading.value = true
  $q.loading.show()
  let query = {grade: activeGrade.value.value, $limit: 1000, school: schoolId.value}
  if (!isAdmin.value) {
    let schoolUserData = await App.service('school-user').get(pub?.user?.schoolUser?._id)
    let classFilter = []
    if (schoolUserData.head?.length > 0) {
      classFilter.push(...schoolUserData.head)
    }
    if (schoolUserData.class?.length > 0) {
      classFilter.push(...schoolUserData.class.filter((e) => e != 'cloudRoom'))
    }
    query._id = {$in: classFilter}
  }
  query.del = false
  const res = await App.service('classes').find({query})
  classList.value = res.data
  getStudents()
  $q.loading.hide()
  loading.value = false
}

const getStudents = async () => {
  for (let i = 0; i < classList.value.length; i++) {
    const item = classList.value[i]
    const standardStudents = await App.service('students').find({query: {class: item._id, $limit: 1000}})
    const joinedStudents = await App.service('students').find({query: {subjectClass: props.subjectClass, $limit: 1000}})
    let list = standardStudents.data.map((item) => {
      return {
        ...item,
        joined: joinedStudents.data.some((joined) => joined._id === item._id),
        checked: joinedStudents.data.some((joined) => joined._id === item._id),
      }
    })
    studentDict.value[item._id] = list
  }
}

const onSelectClass = (item) => {
  activeClass.value = item._id
  studentList.value = studentDict.value[item._id]
}

const onCheckedAll = (e) => {
  studentList.value.forEach((item) => {
    if (!item.joined) {
      item.checked = e
    }
  })
}

const onChecked = (e) => {
  let everyChecked = studentList.value.every((item) => item.checked && !item.joined)
  let everyUnChecked = studentList.value.every((item) => !item.checked && !item.joined)
  if (everyChecked) {
    checkedAll.value = true
  } else if (everyUnChecked) {
    checkedAll.value = false
  } else {
    checkedAll.value = null
  }
}

const onSubmit = async () => {
  $q.loading.show()
  let list = studentList.value.filter((item) => item.checked && !item.joined)
  let classData = await App.service('classes').get(props.subjectClass)
  if (classData.maxParticipants) {
    let count = classData.count?.student || 0
    if (list.length > classData.maxParticipants - count) {
      $q.dialog({
        title: 'You can not proceed this action because it will result in exceeding the max participant no of this class',
        ok: {
          label: 'I got it',
          noCaps: true,
          rounded: true,
        },
      }).onOk(() => {
        onHide()
      })
      $q.loading.hide()
      return
    }
  }
  for (let i = 0; i < list.length; i++) {
    const e = list[i]
    await App.service('students').patch(e._id, {
      $addToSet: {
        subjectClass: props.subjectClass,
      },
      $import: true,
    })
  }
  emit('save')
  $q.notify({type: 'positive', message: 'Student added successfully'})
  $q.loading.hide()
  onHide()
}
</script>
<style lang="scss">
body.screen--sm,
body.screen--md {
  .dialog {
    .q-card {
      min-width: 600px;
    }
  }
}
</style>
