<template>
  <div>
    <PubTopBanner title="Self-enroll application" :isShowMenu="false" />
    <q-page class="column no-wrap pc-max q-px-lg">
      <div class="flex justify-between items-center q-mt-sm">
        <div class="flex items-center">
          <div>Enable student self-enroll</div>
          <q-toggle :disable="!(pub.isSchoolAdmin || isHeadTeacher)" :modelValue="form.enroll.enable" @update:modelValue="(bool) => onToggleEnable(bool)" />
        </div>
        <q-btn v-if="isEditing" rounded flat no-caps label="Cancel" class="text-grey-7" style="border: 1px solid" @click="onCancelClick" />
        <q-btn
          v-else
          rounded
          flat
          no-caps
          label="Edit"
          class="text-teal"
          style="border: 1px solid"
          @click="onEditClick"
          :disable="!form.enroll.enable || !(pub.isSchoolAdmin || isHeadTeacher)" />
      </div>

      <div v-if="form.enroll.enable" class="q-mb-lg">
        <div>
          <DragUploader
            v-if="isEditing"
            @uploaded="(res) => onCoverUploaded(res)"
            fileType="/image*"
            :wrapperStyleObject="{border: errorMap.attachmentsCover ? '2px dashed red' : '2px dashed teal'}" />
          <!-- <q-btn flat rounded dense icon="crop_free" color="teal" style="background: rgba(0, 0, 0, 0.1)" @click="() => onPreview(form.attachmentsCover)" /> -->
          <img style="height: 20rem; width: auto" v-if="form?.attachmentsCover?.hash" :src="hashToUrl(form.attachmentsCover.hash)" class="" />
          <NoData v-else />
        </div>

        <div class="q-my-md">
          <div v-if="isEditing" class="rounded-lg q-pa-md" style="border: 1px solid #aaa">
            <div class="text-subtitle2" :class="[errorMap.classes ? 'text-red' : '']">Choose the students who can self-enroll</div>
            <div>
              <div v-for="item in grades" :key="item._id" class="q-my-sm">
                <div class="text-bold">{{ item?.name }}</div>
                <div v-if="standardClasses.filter((e) => e.grade === item._id)?.length" class="flex items-center">
                  <q-btn
                    v-for="_class in standardClasses.filter((e) => e.grade === item._id)"
                    :key="_class._id"
                    :label="_class?.name"
                    flat
                    dense
                    class="q-ma-sm q-px-sm"
                    :class="[currentClasses.includes(_class._id) ? 'bg-green text-white ' : 'bg-white text-green ']"
                    style="border: 1px solid"
                    @click="() => onClassClick(_class)" />
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="text-subtitle2">Students who can self-enroll</div>
            <div v-if="form?.enroll?.classes?.length" class="flex">
              <q-chip
                v-for="id in form.enroll.classes.filter((e) => Acan.isObjectId(e))"
                :key="id"
                :label="standardClassesMap?.[id]?.name"
                class="bg-green text-white"
                square>
                <q-tooltip anchor="top middle" self="bottom middle">{{ gradesMap?.[standardClassesMap?.[id]?.grade]?.name }}</q-tooltip>
              </q-chip>
            </div>
          </div>
        </div>

        <div class="q-my-md">
          <div class="text-subtitle2" :class="[errorMap.deadline ? 'text-red' : '']">Enroll deadline</div>
          <DateTimePicker v-if="isEditing" :modelValue="`${form.deadline}`" @update:modelValue="onUpdateDeadline" :afterTime="new Date()" />
          <div v-else>
            <div v-if="form?.deadline">{{ date.formatDate(form.deadline, TIME_FORMAT) }}</div>
            <div v-else>/</div>
          </div>
        </div>

        <div class="q-my-md">
          <div class="flex items-center">
            <div class="text-subtitle2">Max participants</div>
            <q-toggle :modelValue="form.maxParticipants !== 0" @update:modelValue="(bool) => onUpdateMaxParticipants(bool)" :disable="!isEditing" />
          </div>
          <q-input v-if="isEditing" type="number" v-model.number="form.maxParticipants" min="1" label="Max participants" outlined />
          <div v-else>{{ form.maxParticipants }}</div>
        </div>

        <div class="q-my-md">
          <div class="flex items-center">
            <div class="text-subtitle2">Approval required</div>
            <q-toggle :modelValue="form.approvalEnable" @update:modelValue="(bool) => onUpdateApprovalEnable(bool)" :disable="!isEditing" />
          </div>
        </div>

        <div class="q-my-md" v-if="form.approvalEnable">
          <div class="text-subtitle2" :class="[errorMap.questions ? 'text-red' : '']">Question for student to answer upon application</div>
          <div v-if="form?.questions?.length">
            <div v-for="question in form.questions" :key="question">
              <div class="item-wrapper flex-inline q-pa-sm">
                <div class="text-grey-7">{{ question }}</div>
                <q-btn
                  v-if="isEditing"
                  rounded
                  flat
                  no-caps
                  icon="close"
                  size="sm"
                  dense
                  class="item-delete-button text-grey-1 bg-red-4"
                  @click="() => onDeleteQuestion(question)" />
              </div>
            </div>
          </div>
          <q-btn
            v-if="isEditing"
            flat
            class="text-teal-4 text-caption bg-white"
            no-caps
            icon="add"
            label="Add questions"
            @click="isQuestionDialogShow = true" />
        </div>

        <q-btn v-if="isEditing" rounded flat no-caps label="Save" class="text-white bg-teal-4 full-width" style="border: 1px solid" @click="onSubmit" />
      </div>

      <!-- dialogs -->

      <!-- <MaterialDialog class="z-max" v-model="isPreviewDialogShow"> -->
      <!--   <MaterialItem :item="currentPreviewData" style="width: 90%; height: 90%" :isPreviewBig="true" :isDialog="true" /> -->
      <!-- </MaterialDialog> -->

      <q-dialog v-model="isQuestionDialogShow" persistent>
        <div class="bg-teal-1 q-pa-md rounded-lg" style="width: clamp(320px, 60%, 480px)">
          <div class="q-px-lg q-pb-lg">
            <q-select
              ref="selectDom"
              dense
              color="orange-9"
              multiple
              use-chips
              use-input
              @input-value="(v) => (search = v.toFirstUpperCase())"
              new-value-mode="add-unique"
              @filter="filterFn"
              v-model="selected"
              :options="options"
              @update:model-value="onUpdated"
              @add="addOption($event)">
              <template v-slot:prepend>
                <span class="text-body2" :class="{'text-negative': selected.length}"> * </span>
              </template>
              <template v-slot:append>
                <q-btn v-if="showCreateBtn()" flat rounded dense color="teal" icon="add" no-caps label="Create" @click="selectDom.add(search, true)" />
              </template>
            </q-select>
          </div>
          <div class="flex justify-end q-gutter-md">
            <q-btn rounded flat no-caps label="Cancel" class="text-grey-7" style="border: 1px solid" @click="onCancelQuestion" />
            <q-btn rounded flat no-caps label="Add" class="bg-teal-4 text-white" @click="onSelectQuestion" />
          </div>
        </div>
      </q-dialog>
    </q-page>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {date} from 'quasar'
import {useRoute} from 'vue-router'

import {TIME_FORMAT} from 'src/boot/const'
import OkDialog from 'src/components/utils/dialogs/OkDialog.vue'
import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'
import DragUploader from 'src/components/utils/inputs/DragUploader.vue'
import DateTimePicker from 'src/components/utils/DateTimePicker.vue'
import {pubStore} from 'stores/pub'
import useSchool from 'src/composables/common/useSchool'
import useClasses from 'src/composables/account/school/useClasses'
import useClassApply from 'src/composables/account/school/useClassApply'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'

const $route = useRoute()
const {userId} = useSchool()
const {patchOneById, getOneById, getListByOption} = useClasses()
const {getQuestionByUid} = useClassApply()
const {getCurriculumListByUid} = useAcademicSetting()
const pub = pubStore()
const isHeadTeacher = ref(false)
const classId = computed(() => $route?.params?.id)

function onToggleEnable(bool) {
  if (bool) {
    turnOnSelfEnroll()
  } else {
    turnOffSelfEnroll()
  }
}

async function turnOnSelfEnroll() {
  const title = ''
  const message = 'Are you sure to turn on student self-enroll'
  const okButtonLabel = 'Yes'
  const cancelButtonLabel = 'Not now'
  $q.dialog({
    component: ConfirmDialog,
    componentProps: {title, message, okButtonLabel, cancelButtonLabel},
  })
    .onOk(async () => {
      $q.loading.show()
      await patchOneById(classId.value, {$set: {'enroll.enable': true}})
      form.value.enroll.enable = true
      $q.notify({type: 'positive', message: 'Self-enrollment has successfully enabled'})
      $q.loading.hide()
    })
    .onCancel(() => {})
    .onDismiss(() => {})
}

async function turnOffSelfEnroll() {
  const title = ''
  const message = 'Are you sure to turn off student self-enroll'
  const okButtonLabel = 'Yes'
  const cancelButtonLabel = 'Not now'
  $q.dialog({
    component: ConfirmDialog,
    componentProps: {title, message, okButtonLabel, cancelButtonLabel},
  })
    .onOk(async () => {
      $q.loading.show()
      await patchOneById(classId.value, {$set: {'enroll.enable': false}})
      form.value.enroll.enable = false
      $q.notify({type: 'positive', message: 'Self-enrollment has successfully disable'})
      $q.loading.hide()
    })
    .onCancel(() => {})
    .onDismiss(() => {})
}

async function showUnfinishedDialog() {
  const title = ''
  const message = 'The required fiels need to be completed before enabling the student self-enroll.'
  $q.dialog({
    component: OkDialog,
    componentProps: {title, message},
  })
    .onOk(async () => {
      $q.loading.show()
      // do something
      $q.loading.hide()
    })
    .onCancel(() => {})
    .onDismiss(() => {})
}

// TODO: #5744, enroll page
// async function showDisableDialog() {
//   const title = ''
//   const message = 'The self-enrollment for this class is disabled.'
//   $q.dialog({
//     component: OkDialog,
//     componentProps: {title, message},
//   })
//     .onOk(async () => {
//       $q.loading.show()
//       // do something
//       $q.loading.hide()
//     })
//     .onCancel(() => {})
//     .onDismiss(() => {})
// }

const grades = ref([])
const gradesMap = computed(() =>
  grades.value.reduce((acc, cur) => {
    acc[cur._id] = cur
    return acc
  }, {})
)
const standardClasses = ref([])
const standardClassesMap = computed(() =>
  standardClasses.value.reduce((acc, cur) => {
    acc[cur._id] = cur
    return acc
  }, {})
)

const form = ref({
  enroll: {enable: false, classes: []},
  attachmentsCover: {},
  deadline: new Date(),
  maxParticipants: 0,
  approvalEnable: false,
  questions: ['Why are you joining us?'],
})

function onUpdateDeadline(v) {
  form.value.deadline = new Date(v)
}

async function getData() {
  const res = await getOneById(classId.value)
  console.warn(res)
  isHeadTeacher.value = res?.host === userId.value
  if (res?.enroll !== undefined) form.value.enroll = res.enroll
  if (res?.attachmentsCover !== undefined) form.value.attachmentsCover = res.attachmentsCover
  if (res?.maxParticipants !== undefined) form.value.maxParticipants = res.maxParticipants
  if (res?.deadline !== undefined) form.value.deadline = res.deadline
  if (res?.approvalEnable !== undefined) form.value.approvalEnable = res.approvalEnable
  if (res?.questions !== undefined) form.value.questions = res.questions
  selected.value = form.value.questions
  currentClasses.value = form.value?.enroll?.classes || []

  const school = res.school
  const curriculum = res.curriculum
  const curriculumList = await getCurriculumListByUid()
  const target = curriculumList?.find((e) => e._id === curriculum)
  const curriculumCode = target?.value

  const gradesRes = await App.service('conf-school').get('get', {query: {key: 'Grades', rid: school}})
  grades.value = gradesRes?.val.filter((e) => e.curriculum?.includes(curriculumCode))
  const standardClassesRes = await getListByOption({school, type: 'standard'})
  standardClasses.value = standardClassesRes?.data || []
}

const isEditing = ref(false)
function onEditClick() {
  isEditing.value = true
}
function onCancelClick() {
  isEditing.value = false
  errorMap.value = {
    attachmentsCover: false,
    classes: false,
    deadline: false,
    questions: false,
  }
}

function onUpdateMaxParticipants(bool) {
  console.log(bool, form.value.maxParticipants)
  if (bool) {
    if (form.value.maxParticipants === 0) {
      form.value.maxParticipants = 1
    }
  } else {
    form.value.maxParticipants = 0
  }
}

function onUpdateApprovalEnable(bool) {
  if (bool) {
    form.value.approvalEnable = true
  } else {
    const title = ''
    const message = 'Switch off will result in all existing data being cleared. Are you sure to proceed?'
    $q.dialog({
      component: OkDialog,
      componentProps: {title, message},
    })
      .onOk(async () => {
        $q.loading.show()
        form.value.approvalEnable = false
        form.value.questions = []
        $q.loading.hide()
      })
      .onCancel(() => {})
      .onDismiss(() => {})
  }
}

async function onCoverUploaded(res) {
  const dto = {
    filename: res?.title?.[userId.value] || res?.title || 'cover',
    mime: res.mime,
    hash: res._id,
  }
  form.value.attachmentsCover = dto
}

const currentClasses = ref([])
function onClassClick(item) {
  const list = currentClasses.value
  const id = item._id
  if (list.includes(id)) {
    currentClasses.value = list.filter((e) => e !== id)
  } else {
    currentClasses.value.push(id)
  }
}

// const currentPreviewData = ref(null)
// const isPreviewDialogShow = ref(false)
// function onPreview(item) {
//   currentPreviewData.value = item
//   isPreviewDialogShow.value = true
// }

const isQuestionDialogShow = ref(false)
const historyQuestions = ref([])
const options = ref([])

const search = ref('')
const selected = ref([])
const selectDom = ref(null)

function filterFn(val = '', update) {
  search.value = val
  update(() => {
    options.value = historyQuestions.value.filter((e) => e.includes(search.value))
  })
}

async function addOption() {
  selectDom.value?.updateInputValue('')
}

async function onUpdated(o) {
  console.log(o)
}

function onSelectQuestion() {
  form.value.questions = selected.value
  isQuestionDialogShow.value = false
}

function onCancelQuestion() {
  selected.value = form.value.questions
  isQuestionDialogShow.value = false
}

function onDeleteQuestion(question) {
  const list = form.value.questions.filter((e) => e != question)
  form.value.questions = list
  selected.value = list
}

function showCreateBtn() {
  let show = true
  if (search.value) {
    let needle = search.value.toLowerCase()
    let list = []
    historyQuestions.value.forEach((item) => {
      list.push(item.toLowerCase())
    })
    if (list.includes(needle)) {
      show = false
    }
  } else {
    show = false
  }
  return show
}

const errorMap = ref({
  attachmentsCover: false,
  classes: false,
  deadline: false,
  questions: false,
})
function checkForm() {
  let isValid = true
  if (!form.value?.attachmentsCover?.hash) {
    isValid = false
    errorMap.value.attachmentsCover = true
  } else {
    errorMap.value.attachmentsCover = false
  }
  if (!form.value?.enroll?.classes?.length) {
    isValid = false
    errorMap.value.classes = true
  } else {
    errorMap.value.classes = false
  }
  if (!form.value?.deadline) {
    isValid = false
    errorMap.value.deadline = true
  } else {
    errorMap.value.deadline = false
  }
  if (form.value?.approvalEnable && !form.value.questions.length) {
    isValid = false
    errorMap.value.questions = true
  } else {
    errorMap.value.questions = false
  }
  return isValid
}

async function onSubmit() {
  const isValid = checkForm()
  if (!isValid) {
    showUnfinishedDialog()
    return
  }
  try {
    $q.loading.show()
    form.value.enroll.classes = currentClasses.value
    await patchOneById(classId.value, form.value)
    await getData()
  } catch (error) {
    console.error(error)
  } finally {
    $q.loading.hide()
    isEditing.value = false
  }
}

onMounted(async () => {
  await getData()
  const res = await getQuestionByUid(userId.value)
  const questions = res?.questions || []
  historyQuestions.value = questions
  options.value = questions
})
</script>

<style lang="scss">
.item-wrapper {
  position: relative;
  &:hover {
    .item-delete-button {
      opacity: 1;
    }
  }
  .item-delete-button {
    position: absolute;
    top: -0.35rem;
    right: -0.65rem;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    cursor: pointer;
  }
}
</style>
