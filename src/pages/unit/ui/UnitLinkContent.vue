<template>
  <q-expansion-item
    class="shadow-2 rounded-borders-md overflow-hidden q-my-md"
    v-for="group in unit.linkGroup.filter((v) => v.link.length > 0)"
    :key="group._id">
    <template v-slot:header>
      <q-item-section>
        {{ group.name }}
      </q-item-section>
      <q-item-section side>
        <div class="row items-center">
          <q-icon name="dynamic_feed"></q-icon>
          <span class="q-pl-sm">{{ group.link.length }} Lesson</span>
        </div>
      </q-item-section>
    </template>
    <template v-if="unit.one.mode.toLowerCase().includes('unit')">
      <template v-for="link in group.link" :key="link._id">
        <q-separator />
        <ListItemCard v-if="link.doc" :doc="link.doc" :pid="unit.one._id" dense />
      </template>
    </template>
    <template v-else>
      <q-expansion-item v-for="link in group.link" :key="link._id">
        <template v-slot:header>
          <q-item-section>
            <div class="row items-center">
              <q-avatar size="1.8rem" :color="UnitModeMap[link.mode].color" text-color="white" :icon="UnitModeMap[link.mode].icon" />
              <span class="q-pl-sm">{{ link.doc.name }}</span>
            </div>
          </q-item-section>
        </template>
        <div class="q-pa-md">
          <q-card bordered class="shadow-0 rounded-borders-md q-pa-md row">
            <q-btn
              class="col-xs-4 col-sm-3 col-md-3 q-pa-md"
              label="Overview"
              stack
              flat
              icon="o_visibility"
              no-caps
              :to="unit.viewRoute({link, _id: link.id}, pid, 'overview')"></q-btn>
            <q-btn
              v-if="link.mode.toLowerCase().includes('task')"
              class="col-xs-4 col-sm-3 col-md-3"
              label="Slides"
              stack
              flat
              icon="slideshow"
              no-caps
              :to="unit.viewRoute({link, _id: link.id}, pid, 'slides')"></q-btn>
          </q-card>
        </div>
      </q-expansion-item>
    </template>
  </q-expansion-item>
</template>
<script setup>
import ListItemCard from './ListItemCard.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
import {ref} from 'vue'
const pid = ref(route.params.id !== unit.one._id ? route.params.id : null)

import {serviceAuthStore} from 'stores/service-auth'
// library 课件不需要查询认证项
const serviceAuth = unit.isLib ? null : serviceAuthStore()
</script>
