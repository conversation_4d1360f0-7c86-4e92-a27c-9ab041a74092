<template>
  <q-layout view="hHh Lpr lff" class="overflow-hidden">
    <q-header elevated class="bg-white text-black">
      <div class="row no-wrap">
        <q-toolbar class="col-md-3 col-sm">
          <q-btn flat @click="drawer = !drawer" round dense icon="menu" class="hidden" />
          <q-btn flat round dense icon="navigate_before" @click="onBackClick"></q-btn>
          <q-toolbar-title>Import Premium Content</q-toolbar-title>
        </q-toolbar>
        <div class="col gt-sm"></div>
        <div class="row col-3 q-pr-md flex items-center">
          <q-space></q-space>
        </div>
      </div>
    </q-header>

    <q-page-container>
      <q-page class="pc-max">
        <div class="row relative-position">
          <div class="col-12 col-sm-6">
            <div class="q-pt-md q-px-md" v-if="!$route.query.authId">
              <GeneralFilters v-model="filters" input-only :options="filterOptions" @update:modelValue="requery"></GeneralFilters>
              <div class="q-gutter-sm">
                <q-btn
                  v-for="(o, i) in modeOptions"
                  :key="i"
                  :color="o.value === mode ? 'primary' : 'grey'"
                  @click="find(o.value)"
                  :label="o.label"
                  no-caps
                  :disable="loading"
                  rounded />
              </div>
            </div>
            <div v-if="!isEmpty(linkList)" :style="`height: calc(100vh - 180px)`" class="overflow-auto q-px-md q-pt-sm" id="scroll-area-with-virtual-scroll-1">
              <q-virtual-scroll
                separator
                scroll-target="#scroll-area-with-virtual-scroll-1"
                component="q-list"
                class="col full-width overflow-auto q-pb-xl q-mb-xl"
                :items="linkList"
                @virtual-scroll="onVirtualScroll">
                <template v-slot:after>
                  <div v-if="!route.query.authId && dataSize === limit" class="row justify-center q-my-md">
                    <q-spinner-ball color="primary" size="2em" class="full-width" />
                  </div>
                  <div v-else class="q-pa-md text-grey text-center hidden">It's over</div>
                </template>
                <template v-slot="{item: o}">
                  <div class="q-my-sm" @click.stop="onLinkClick(o)">
                    <ListItemCard v-if="$route.query.authId" :doc="o" nolink :class="activedLink?._id === o._id ? 'bg-teal-1 q-border-3-primary' : ''" />
                    <AuthItemCard v-else :authDoc="o" :subjectMaps="subjectMaps" :class="activedLink?._id === o._id ? 'bg-teal-1 q-border-3-primary' : ''" />
                  </div>
                </template>
              </q-virtual-scroll>
            </div>
            <template v-else>
              <div v-if="loading" class="text-center q-pa-xl text-grey">
                <q-spinner-ball color="primary" size="2em" class="full-width" />
              </div>
              <NoData v-else />
            </template>
          </div>
          <div class="col-12 col-sm-6 gt-xs">
            <q-scroll-area :style="`height: calc(100vh - 120px)`">
              <div class="bg-white border-1-grey rounded-borders q-ma-md q-pa-md q-mb-xl q-pb-xl">
                <div v-if="activeLoading" class="text-center q-pa-xl text-grey">
                  <q-spinner-ball color="primary" size="2em" class="full-width" />
                </div>
                <div v-else-if="unit.one && $q.screen.gt.xs">
                  <q-img v-if="unit.isUnit" :src="unit.one.cover"></q-img>
                  <q-tabs v-else-if="unit.isTask" v-model="tab" inline-label active-class="text-primary">
                    <q-tab class="q-px-sm" v-for="(o, i) in ['Overview', 'Slides']" :key="i" :label="o" :name="o" no-caps></q-tab>
                  </q-tabs>
                  <VideoPreviewPage v-if="unit.isVideo" isPreview :key="activedLink._id" :height="'400px'" :videoId="activedLink.video" />
                  <div v-else-if="tab === 'Overview'">
                    <OverView :one="unit.one" />
                    <UnitLinkContent v-if="unit.isTask" />
                  </div>
                  <SlidesUI v-else-if="tab === 'Slides'"></SlidesUI>
                </div>
                <NoData v-else messageColor="grey" size="9rem" message="Please choose one content from the left list to view the details"></NoData>
              </div>
            </q-scroll-area>
          </div>
        </div>
      </q-page>
    </q-page-container>
    <q-footer reveal elevated class="bg-white">
      <q-toolbar>
        <q-space></q-space>
        <q-btn
          class="q-mx-sm"
          :class="{'full-width': $q.screen.lt.sm}"
          color="primary"
          label="Next"
          :disable="!unit.one"
          rounded
          no-caps
          @click="onNextOrSave()"></q-btn>
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>

<script setup>
import {unitStore} from 'stores/unit'
const unit = unitStore()

import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import AuthItemCard from 'pages/unit/ui/AuthItemCard.vue'
import ListItemCard from 'pages/unit/ui/ListItemCard.vue'
import UnitLinkContent from 'pages/unit/ui/UnitLinkContent.vue'
import OverView from 'pages/unit/ui/OverView.vue'
import SlidesUI from 'pages/unit/ui/SlidesUI.vue'
import GeneralFilters from 'components/GeneralFilters.vue'
import VideoPreviewPage from 'pages/InteractiveVideo/PreviewPage.vue'

const tab = ref('Overview')
const route = useRoute()
const router = useRouter()
const loading = ref(true)
const activedLink = ref(null)
const linkList = ref([])
const query = ref({})

const filters = ref({})
const filterOptions = ref([])

const modeOptions = [
  {label: 'Unit', value: 'unit'},
  {label: 'Task', value: 'task'},
  {label: 'Video', value: 'video'},
]
const mode = ref('task')
const modeMap = {task: ['task', 'pdTask'], unit: ['unit', 'pdUnit'], video: ['video']}

const activeDoc = ref(null)
const activeLoading = ref(false)
let authDoc
async function getAuthDoc(authId) {
  activeLoading.value = true
  authDoc = await App.service('service-auth')
    .get(authId)
    .catch(() => {
      $q.notify({type: 'negative', message: 'Current content is not available, please select another one.'})
      requery()
      return null
    })
  const {unitSnapshot, linkSnapshot} = authDoc
  activeDoc.value = authDoc
  if (unitSnapshot) unit.setAuthUnit(unitSnapshot, linkSnapshot)
  activeLoading.value = false
}
const onLinkClick = async (link) => {
  // 子课件不需要请求
  if (route.query.authId) {
    activedLink.value = link
    unit.setAuthUnit(link, authDoc.linkSnapshot)
    return
  }
  if (activedLink.value?._id === link._id) return // 重复点击
  activeDoc.value = null
  activedLink.value = link
  await getAuthDoc(link._id)
}

function clear() {
  activedLink.value = null
  linkList.value.length = 0
  activeDoc.value = null
  activedLink.value = null
  query.value.$skip = 0
  unit.clear()
}
let limit = ref(10)
let dataSize = ref(0)
const find = async (val) => {
  if (val) {
    mode.value = val
    clear()
  }

  loading.value = true
  query.value.status = 2
  query.value.$sort = {updatedAt: -1}
  query.value['unitSnapshot.mode'] = {$in: modeMap[mode.value]}
  const queryExt = {}
  if (mode.value === 'task') queryExt['unitSnapshot.sessionType'] = 'live'
  if (filters.value.search) queryExt['unitSnapshot.name'] = {$regex: filters.value.search, $options: 'i'}
  const list = await App.service('service-auth').get('cloudList', {query: {...query.value, ...queryExt}})
  loading.value = false
  dataSize.value = 0
  if (!list.data) return
  dataSize.value = list.data.length
  limit.value = list.limit
  list.data.map((item) => {
    const price = item.unitSnapshot?.questions?.length * 50
    item.unitSnapshot.price = +(price / 100).toFixed(2)
    item.unitSnapshot.authId = item._id
    linkList.value.push(item)
  })
}

const requery = async () => {
  clear()
  await find()
}

const onVirtualScroll = async ({index, to}) => {
  if (route.query.authId) return // unit子课件不加载数据
  console.log('onVirtualScroll')
  if (index !== to) return // console.warn(index, to, direction)
  if (dataSize.value < limit.value) return // has last page
  console.warn('need load more')
  query.value.$skip += limit.value
  await find()
}

const onBackClick = () => {
  if (route.query.back) {
    router.replace(decodeURIComponent(route.query.back))
  } else {
    router.back()
  }
}

function toUnit() {
  const {linkSnapshot} = activeDoc.value
  clear()
  for (const key in linkSnapshot) {
    if (linkSnapshot[key].sessionType !== 'live') continue
    linkList.value.push(linkSnapshot[key])
  }
}

const onNextOrSave = () => {
  if (!activedLink.value && !unit.one?._id) return null
  if (unit.isUnit) {
    router.push({query: {authId: activedLink.value._id}})
    toUnit()
    return
  }
  const {start, end, authId} = route.query
  const query = {start, end}
  if (authId) query.unitId = activedLink.value._id
  router.push({
    path: `/com/schedulePremiumWorkshop/${authId || activedLink.value._id}`,
    query,
    hash: route.hash,
  })
}

import {subjectsStore} from 'stores/subjects'
const subjects = subjectsStore()
const subjectMaps = ref({})
onMounted(async () => {
  subjectMaps.value = await subjects.getOptionList('1')
  if (route.query.authId) {
    await getAuthDoc(route.query.authId)
    toUnit()
  }
})
</script>
<style lang="sass" scope>
.border-1-grey
  border: 1px solid $grey-4
.border-1
  border: 1px solid #49bbbd4d
.border-05
  border: 0.5px solid #D7E3F1
.z-up
  z-index: 10 !important
</style>
