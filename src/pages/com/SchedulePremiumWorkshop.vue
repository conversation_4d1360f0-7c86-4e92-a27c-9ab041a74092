<template>
  <q-layout view="hHh LpR fFf">
    <q-header elevated class="bg-white text-black">
      <div class="row no-wrap">
        <q-toolbar class="col-3">
          <q-btn flat @click="leftDrawerOpen = !leftDrawerOpen" round dense icon="menu" class="lt-sm" />
          <q-btn flat round dense icon="navigate_before" @click="onBackClick"></q-btn>
        </q-toolbar>
        <div class="col flex items-center">
          <q-toolbar-title class="text-center" v-if="step">{{ step == 1 ? 'Schedule' : isTool ? 'Assessment tool' : 'Self-study sessions' }}</q-toolbar-title>
        </div>
        <div class="row col-3 q-pr-md flex items-center">
          <q-space></q-space>
          <pubAvatar :src="pub.user?.avatar" size="2rem"></pubAvatar>
        </div>
      </div>
    </q-header>
    <q-drawer v-if="step == 1" show-if-above :width="250" :breakpoint="600" v-model="leftDrawerOpen" side="left" bordered class="bg-blue-grey-1">
      <template v-if="step == 1">
        <div class="q-px-md text-primary gt-xs" v-if="liveList.length && noneScheduled()">
          <div class="q-mt-md">Please drag the live session from the left bar to the calendar to schedule the time</div>
        </div>
        <div class="q-pa-md" v-if="isTask && liveList.length">
          <div v-for="(link, lidx) in liveList" :key="lidx">
            <div
              class="q-mt-md q-pa-sm rounded-borders"
              :class="linkClasses(link)"
              :draggable="!link.scheduled && !link.readonly"
              @click="onLinkClicked(link)"
              @dragstart="onDragStart($event, link, lidx)">
              <q-icon v-if="link.scheduled || link.readonly" name="svguse:/v2/icons.svg#bookopen"></q-icon>
              <q-icon v-else name="hourglass_empty"></q-icon>
              {{ link.name }}
            </div>
          </div>
        </div>
        <div class="" v-else v-for="(group, index) in groups" :key="index">
          <q-expansion-item expand-separator v-if="getLiveLinksByGroup(group)?.length">
            <template v-slot:header>
              <q-item-section class="text-bold"> {{ group.name }}</q-item-section>
            </template>
            <div v-for="(link, lidx) in getLiveLinksByGroup(group)" :key="lidx">
              <div
                class="q-mx-md q-my-sm q-pa-sm rounded-borders"
                :class="linkClasses(link)"
                :draggable="!link.scheduled && !link.readonly"
                @click="onLinkClicked(link)"
                @dragstart="onDragStart($event, link, lidx)">
                <q-icon v-if="link.scheduled || link.readonly" name="svguse:/v2/icons.svg#bookopen"></q-icon>
                <q-icon v-else name="hourglass_empty"></q-icon>
                {{ link.name }}
              </div>
            </div>
          </q-expansion-item>
        </div>
        <ScheduleOptions
          v-if="$q.screen.gt.xs"
          v-model="form"
          :one="one"
          :onParticipantClick="onParticipantClick"
          :onClassChanged="onClassChanged"
          :classDetails="classDetails"
          :classesOptions="classesOptions"
          :teacherOrGradeDetails="teacherOrGradeDetails"
          :gradeOptions="gradeOptions"
          :isPublic="isPublic"
          :isSelfStudy="!liveList.length"
          :isEducator="isEducator"
          :isPersonal="isPersonal"
          :classIsAssigned="classIsAssigned"
          :isImportFromMyServicePage="isImportFromMyServicePage"
          :defaultOpenZoom="defaultOpenZoom"
          :disableParticipant="disableParticipant"
          :disableGrade="disableGrade"
          :disableClass="disableClass"
          :disableStudents="disableStudents"
          :disableZOOM="disableZOOM"
          :disableMin="disableMin"
          :disableMax="disableMax"
          :canCloseZoom="canCloseZoom"></ScheduleOptions>
        <div class="q-pt-md hidden" v-if="isEducator">
          <q-separator v-if="liveList.length"></q-separator>
          <div class="row q-pa-md">
            <div class="col">Allow participants to download the resource</div>
            <q-toggle class="" v-model="form.allowdown" color="primary" />
          </div>
        </div>
        <div class="q-my-lg"></div>
      </template>
      <template v-else-if="step == 2">
        <div style="min-height: 60vh">
          <div v-if="liveList.length != linkList.length">
            <div class="q-pa-md text-h6">Setting for selected {{ isTool ? 'assessment tool' : 'sessions' }}</div>
            <q-separator></q-separator>
            <div v-if="showLiveSessionTip">
              <div class="q-pa-md">The starting and ending time can not be changed. You can select the session for block setting and category setting.</div>
            </div>
            <template v-else>
              <div v-if="!hasStartActive && !hasEndActive">
                <div class="q-pa-md">Starts when</div>
                <div class="row q-px-md">
                  <div class="col q-pl-lg">All the self-study sessions are now set to start when the previous session ends(Default)</div>
                  <div class="flex items-center">
                    <q-icon size="1.2rem" color="grey-7" name="o_contact_support">
                      <q-tooltip max-width="260px">
                        The previous session refers to any previous live session OR the start time will be set as current time if there is no live session
                        scheduled prior to it.
                      </q-tooltip>
                    </q-icon>
                  </div>
                </div>
              </div>
              <div v-if="hasStartActive">
                <div class="q-pa-md">Starts when</div>
                <div class="q-px-md">
                  <input
                    ref="flatStartRef"
                    id="flat-start"
                    class="col date-input full-width"
                    :class="{'border-negative': setStartDateFirst}"
                    @change="onStartDateUpdate"
                    placeholder="Customize time"
                    v-model="startDate" />
                  <div class="text-negative q-py-sm" v-if="setStartDateFirst">Please set start time first</div>
                </div>
                <template v-if="hasPreviousLiveSession">
                  <div class="q-py-md text-center text-body">-- Or --</div>
                  <div class="q-px-sm q-pb-md" v-if="!isTool">
                    <q-checkbox v-model="startOption" label="The previous live session ends" @click="onStartOptionClick" />
                    <q-icon size="1.2rem" class="q-ml-sm" color="grey-7" name="o_contact_support">
                      <q-tooltip max-width="260px">
                        The previous session refers to any previous live lesson which has a set ending time.Please notice that if there is no set ending time
                        before the selected session, then by default it will be set as the current time.
                      </q-tooltip>
                    </q-icon>
                  </div>
                </template>
                <q-separator v-if="hasStartActive && hasEndActive" inset class="q-mt-md"></q-separator>
              </div>
              <div class="relative-position" v-if="hasEndActive">
                <div v-if="disableEndOpion" class="fit absolute-top" style="z-index: 101; cursor: not-allowed" @click="onEndSectionClicked">
                  <q-tooltip> Please set a start time. </q-tooltip>
                </div>
                <div class="q-px-md q-pt-md">Ends when</div>
                <div class="q-px-sm">
                  <q-radio :disable="disableEndOpion" v-model="endOption" val="deadline" @update:modelValue="onEndOptionUpdate" label="deadline reaches" />
                </div>
                <div class="q-px-sm q-pb-md">
                  <q-radio :disable="disableEndOpion" v-model="endOption" val="countdown" @update:modelValue="onEndOptionUpdate" label="countdown ends" />
                </div>
                <div class="q-px-md row" v-show="endOption == 'deadline'">
                  <input ref="flatEndRef" id="flat-end" class="col date-input" @change="onEndDateUpdate" placeholder="Set deadline" v-model="endDate" />
                </div>
                <div class="q-px-md row" v-show="endOption == 'countdown'">
                  <q-select
                    class="full-width"
                    ref="countdownRef"
                    map-options
                    emit-value
                    outlined
                    dense
                    @update:modelValue="onCountdownUpdate"
                    v-model="selectedCountdown"
                    :options="countdownOptions"></q-select>
                </div>
              </div>
            </template>
          </div>
          <div v-else class="q-pa-md text-primary">There is no self-study session under this Unit, please click launch to complete scheduling</div>
        </div>
        <!--
        <q-separator class="q-my-md"></q-separator>
        <div class="row q-px-md q-pt-md">
          <div class="">Task Categories</div>
          <q-space></q-space>
          <q-btn v-if="taskCategories.length" color="grey-7" dense flat size=".7rem" @click="showTaskCategory = true" icon="o_edit"></q-btn>
        </div>
        <div class="q-pa-md text-weight-medium" v-if="!taskCategories.length">
          You have not set any task category yet, please click to set first.
          <div class="text-center q-mt-md">
            <q-btn flat size=".7rem" icon="add_circle_outline" @click="showTaskCategory = true"></q-btn>
          </div>
        </div>
        <div v-else class="q-px-md q-pt-md row" v-for="(category, index) in taskCategories" :key="index">
          <div class="text-white q-px-md q-py-xs rounded-borders" :class="`bg-${category.color}`">{{ category.name }}</div>
          <q-space></q-space>
          <div>{{ category.weight }}</div>
        </div>
        <div class="text-center q-pa-md" v-if="taskCategories.length == 0">
        </div>
        -->
      </template>
    </q-drawer>
    <q-page-container class="schedule-course-container">
      <q-resize-observer @resize="onResize" />
      <q-page class="flex justify-center items-start">
        <div class="" style="width: 100%; max-width: 1040px">
          <div class="q-pa-md full-width">
            <q-spinner-ball color="primary" size="2em" class="full-width" v-if="loading" />
          </div>
          <q-stepper
            v-model="step"
            @transition="onStepperTransition"
            :class="{'schedule-stepper': !$q.screen.gt.sm}"
            header-nav
            ref="stepper"
            color="primary"
            animated>
            <q-step :name="0" title="Select campaign settings" icon="settings">
              <ScheduleOptions
                v-model="form"
                :onParticipantClick="onParticipantClick"
                :onClassChanged="onClassChanged"
                :classDetails="classDetails"
                :classesOptions="classesOptions"
                :teacherOrGradeDetails="teacherOrGradeDetails"
                :gradeOptions="gradeOptions"
                :isPublic="isPublic"
                :isSelfStudy="!liveList.length"
                :isEducator="isEducator"
                :isPersonal="isPersonal"
                :classIsAssigned="classIsAssigned"
                :isImportFromMyServicePage="isImportFromMyServicePage"
                :defaultOpenZoom="defaultOpenZoom"
                :disableParticipant="disableParticipant"
                :disableGrade="disableGrade"
                :disableClass="disableClass"
                :disableStudents="disableStudents"
                :disableZOOM="disableZOOM"
                :disableMin="disableMin"
                :disableMax="disableMax"
                :canCloseZoom="canCloseZoom"></ScheduleOptions>
            </q-step>

            <q-step class="q-pt-md" :name="1" title="Select campaign settings" icon="settings">
              <CalendarCenter
                v-model="calendarSessions"
                scheduled
                :show-deadline="showDeadline"
                :reg-date="form.regDate"
                :live-start="allLiveStart"
                :underscore-id="$route.params._id"
                :current-session="currentSession"
                @change="onCalendarChange"
                @hide="onCalendarHide"
                @save="onCalendarSave"
                @touch="onCalendarTouch"
                @toggle="onCalendarToggle"
                @remove="onSessionRemove" />
            </q-step>

            <q-step :name="2" title="Create an ad group" caption="Optional" icon="create_new_folder">
              <div class="column" style="height: calc(100vh - 120px)">
                <div class="q-mb-md row hidden">
                  <q-checkbox @update:model-value="onAllClicked" v-model="checkAllSessions" v-if="!isImportToSession">All</q-checkbox>
                  <q-space></q-space>
                  <template v-if="checkedLinkContents.length">
                    <q-btn-dropdown outline rounded color="grey-7" label="Block setting" icon="auto_graph" no-caps>
                      <q-list style="max-width: 300px">
                        <q-item clickable v-close-popup @click="onBlockSetting(true)">
                          <q-item-section avatar>
                            <q-icon name="o_lock"></q-icon>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label>Block selected ones</q-item-label>
                            <q-item-label caption> All students will be blocked upon entering the session, you might unblock them afterward </q-item-label>
                          </q-item-section>
                        </q-item>

                        <q-item clickable v-close-popup @click="onBlockSetting(false)">
                          <q-item-section avatar>
                            <q-icon name="o_lock_open" color="primary"></q-icon>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label class="text-primary">Unblock selected ones</q-item-label>
                            <q-item-label caption> All students can enter the session within the class number limits</q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </q-btn-dropdown>
                    <CategorySetting @confirm="onCategorySettingConfirm"></CategorySetting>
                  </template>
                </div>
                <q-scroll-area class="col full-width">
                  <div v-for="(group, index) in groups" :key="index">
                    <div class="shadow-3 q-pa-sm q-ma-md rounded-borders-md">
                      <draggable
                        class="q-list q-list--separator"
                        ghost-class="ghost"
                        :list="getMainLinksByGroup(group)"
                        @end="onSessionDragEnd"
                        @start="onSessionDragStart"
                        :data-group="group._id"
                        handle=".handle"
                        itemKey="_id">
                        <template #item="{element}">
                          <div :id="element._id">
                            <template v-for="(sublink, iidx) in getSubLinksOf(element, 'Preparations')" :key="iidx">
                              <LinkContent
                                @change="onLinkContentChange"
                                @sync="onLinkContentSync"
                                child
                                before
                                :categories="!targetSession?.booking"
                                :first="iidx == 0"
                                :link="sublink"
                                :links="linkListSorted"
                                :groups="groups">
                              </LinkContent>
                            </template>
                            <LinkContent
                              @change="onLinkContentChange"
                              @sync="onLinkContentSync"
                              :categories="!targetSession?.booking"
                              :sublinks="getSubLinks(element)"
                              :link="element"
                              :links="linkListSorted"
                              :groups="groups">
                            </LinkContent>
                            <template v-for="(sublink, iidx) in getSubLinksOf(element, 'After')" :key="iidx">
                              <LinkContent
                                @change="onLinkContentChange"
                                @sync="onLinkContentSync"
                                child
                                after
                                :categories="!targetSession?.booking"
                                :last="iidx + 1 == getSubLinksOf(element, 'After').length"
                                :link="sublink"
                                :links="linkListSorted"
                                :groups="groups">
                              </LinkContent>
                            </template>
                          </div>
                        </template>
                        <template #header>
                          <q-item>
                            <q-item-section>
                              <q-item-label>
                                <span class="text-h6">
                                  {{ group.name }}
                                </span>
                              </q-item-label>
                            </q-item-section>
                          </q-item>
                        </template>
                      </draggable>
                    </div>
                    <!--
                  <NoData v-if="!getMainLinksByGroup(group)?.length"></NoData>
                  <template v-else>
                    <div class="row flex items-center q-pb-xs" v-if="group._id != 'default'">
                      <div class="text-h6" v-if="!['pdTask', 'task'].includes(one.mode)">{{ group.name }}</div>
                      <q-space></q-space>
                      <div class="hidden cursor-pointer" @click="onGroupAllClick(group)" v-if="liveList.length != linkList.length">
                        <q-icon class="q-pr-xs" :name="group.allActive ? 'check_box' : 'check_box_outline_blank'" color="primary" size="1.4rem"></q-icon>All
                      </div>
                    </div>
                    <div class="border-grey q-pa-md q-mb-lg rounded-borders">
                      <template v-for="(link, lidx) in getMainLinksByGroup(group)" :key="lidx">
                        <LinkContent @active="onLinkContentActive" :categories="taskCategories" :item="link"> </LinkContent>
                        <template v-for="(item, iidx) in getSubLinksOf(link)" :key="iidx">
                          <LinkContent
                            @active="onLinkContentActive"
                            :categories="taskCategories"
                            child
                            :last="iidx + 1 == getSubLinksOf(link).length"
                            :item="item">
                          </LinkContent>
                        </template>
                      </template>
                    </div>
                  </template>
                  --></div>
                </q-scroll-area>
              </div>
            </q-step>
          </q-stepper>
        </div>
      </q-page>
    </q-page-container>
    <q-footer reveal elevated class="bg-white">
      <q-toolbar>
        <q-space></q-space>
        <q-btn
          v-if="step == 0"
          class="q-mx-sm"
          :class="{fit: $q.screen.lt.sm}"
          color="primary"
          :disable="!readyToNext()"
          label="Next"
          icon="svguse:/v2/icons.svg#check"
          rounded
          no-caps
          @click="onNextBtnClick"></q-btn>
        <q-btn
          v-if="step == 1"
          class="q-mx-sm"
          :class="{fit: $q.screen.lt.sm}"
          color="primary"
          :disable="!readyToNext()"
          label="Next"
          icon="svguse:/v2/icons.svg#check"
          rounded
          no-caps
          @click="onNextBtnClick"></q-btn>
        <q-btn
          v-if="step == 2"
          class="q-mx-sm"
          :class="{fit: $q.screen.lt.sm}"
          color="primary"
          :disable="!readyToLaunch()"
          :label="isReschedule ? 'Update' : 'Launch'"
          icon="svguse:/v2/icons.svg#check"
          :loading="launching"
          rounded
          no-caps
          @click="onLaunchBtnClick"></q-btn>
      </q-toolbar>
    </q-footer>

    <q-dialog v-model="zoomAlert" persistent>
      <q-card>
        <q-card-section style="max-height: 50vh" class="scroll">
          <div class="text-subtitle2">
            Due to improper operation leading to Zoom authorization failure(Click confirm without checking all the authorization items)
          </div>

          <q-img src="~/assets/img/zoomguide1.png"></q-img>
          <div class="text-subtitle2 q-py-sm">
            Due to improper operation leading to Zoom authorization failure(Click confirm without checking all the authorization items), please follow the steps
            below to complete the scheduling.
            <div>1.Delete the scheduled content;</div>
            <div>2.Go to the account page to disconnect Zoom then reconnect Zoom(ensure that Zoom authorization is correctly completed);</div>
            <div>3.Reschedule this content.</div>
          </div>

          <q-img src="~/assets/img/zoomguide.png"></q-img>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn rounded class="full-width" no-caps label="I got it" icon="check" @click="routerReplace(zoomAlertHref)" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="packageAlert" persistent>
      <q-card>
        <q-card-section class="scroll">
          <div class="text-subtitle2">Add "New Prompt" to promote in class</div>

          <q-img class="q-my-sm" src="~/assets/img/newprompt.jpg"></q-img>
          <div class="text-subtitle2 q-py-sm">
            You can add prompting slides by clicking “new prompt” bottom in the classroom to promote the linked premium workshop or bundled service package, as
            well as displaying promotional materials. Participants will be able to enrol/purchase the product via the sidebar during the class once new prompt
            inserted.
          </div>
          <q-radio v-model="noPackageTip" val="true"> Do not remind me again</q-radio>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn rounded class="full-width" no-caps label="I got it" icon="check" @click="onPackageTipClick" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
    <q-dialog v-model="purposeAlert">
      <q-card style="width: 580px">
        <q-card-section class="scroll">
          <div class="text-subtitle2 q-pb-md">Please choose the purpose of current workshop</div>
          <ChipOptions v-model="purpose" type="radio" :options="purposeOptions"></ChipOptions>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn rounded no-caps label="Cancel" outline color="grey-7" v-close-popup />
          <q-btn rounded no-caps label="Confirm" :disable="!purpose?.value" @click="onPurposeConfirm" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-layout>
</template>

<script setup>
/*
  imports
*/
import {ref, onMounted, computed, inject} from 'vue'
import {LocalStorage, date} from 'quasar'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import CategorySetting from 'components/CategorySetting.vue'
import CalendarCenter from 'components/CalendarCenter.vue'
import RecommendDialog from 'components/RecommendDialog.vue'
import ChipOptions from 'src/components/ChipOptions.vue'
import LinkContent from 'components/LinkContent.vue'
import ScheduleOptions from 'components/ScheduleOptions.vue'
import useStudents from 'src/composables/account/school/useStudents'
import useGrade from 'src/composables/account/academic/useGrade'
// import useSubject from 'src/composables/account/academic/useSubject'
import useSchoolUser from 'src/composables/account/school/useSchoolUser'
import useUnit from 'src/composables/account/unit/useUnit.js'
import {subjectsStore} from 'stores/subjects'
import draggable from 'vuedraggable'
import {calPremiumMentor} from 'src/pages/order/consts'
import {calServiceDiscount} from 'src/pages/sys/package/const'

/*
  consts
*/
const pub = pubStore()
const route = useRoute()
const router = useRouter()
const subjects = subjectsStore()
const servicePack = {}
const promotionSession = ref(null)
const isPromotion = ref(false)
const isPremium = ref(false)
const serviceTypes = ref([])
const colors = inject('taskCategoryColors')
const contentsType = inject('ContentsType')
const leftDrawerOpen = ref(false)
const isTask = ref(false)
const isUnit = ref(false)
const isVideo = ref(false)
const isPublic = ref(false)
const isEducator = ref(false)
const isImportFromMyServicePage = ref(false)
const isTool = ref(false)
const isPersonal = ref(true)
const isImportToSession = ref(false)
const isReschedule = ref(false)
const isLiveUnit = ref(false)
const targetSessionId = ref(null)
const targetSessionGroup = ref(null)
const checkAllSessions = ref(false)
const groups = ref([])
const touchScope = ref(null)
const zoomConnected = ref(true)
const zoomAlert = ref(false)
const zoomAlertHref = ref(false)
const packageAlert = ref(false)
const purposeAlert = ref(false)
const noPackageTip = ref(false)
const packageTipKey = 'SERVICE_PACKAGE_TIPS'
let calendarListQuery = {}
let calendarListSessions = null
let calendarListMyLiveSessions = null
let showAllMyLiveSessions = false
//for import
const targetSession = ref({})
const groupSessions = ref([])
const allSessions = ref([])
const classesList = ref([])
const classIsAssigned = ref(false)
const {list: studentsList, getList: getStudentsList} = useStudents()
const {list: gradeList} = useGrade()
// const {getSubjectCodeById} = useSubject()
const {list: teachersList} = useSchoolUser()
const {getOneById, relateLinkList, allRelateLinkList} = useUnit()
const purpose = ref({})
const purposeOptions = ref([
  {label: 'Teaching/Training', value: 'paid', tips: 'This workshop will be scheduled as a paid session for Teaching/Training purpose.'},
  {label: 'Promotional', value: 'promotion', tips: 'I would like to use this session to promote other courses or products on Classcipe'},
])

const form = ref({
  name: '',
  image: '',
  price: '100.00',
  discount: 10,
  start: null,
  end: null,
  hasError: false,
  participant: 'class',
  selectedPeople: [],
  selectedClass: null,
  selectedTeachersOrGrades: [],
  selectedStudents: [],
  zoom: {
    capacity: 0,
    checked: false,
    passcode: false,
    invalid: false,
    waiting_room: true,
  },
  status: null,
  regDate: null,
  isPaid: false,
  isMax: true,
  isGroup: false,
  group: 1,
  regMax: 100,
  allowdown: false,
})

const showDeadline = ref(false)
const allLiveStart = ref(null)
const currentSession = ref(null)
const disableZOOM = ref(false)
const disableParticipant = ref(false)
const disableClass = ref(false)
const disableGrade = ref(false)
const disableStudents = ref(false)
const disableMin = ref(false)
const disableMax = ref(false)
const defaultOpenZoom = ref(false)
const canCloseZoom = ref(false)

const calendarStartDate = ref(null)
const calendarEndDate = ref(null)

const startDate = ref(null)
const _startDate = ref(null)
const endDate = ref(null)
const flatEndRef = ref(null)
const flatStartRef = ref(null)
const countdownRef = ref(null)

const one = ref({})
const linkList = ref([])
const liveList = ref([])

const classesOptions = ref([])
const classDetails = ref([])
const teacherOrGradeDetails = ref([])
const gradeOptions = ref([])

const selectedCountdown = ref(null)
const countdownOptions = ref([
  {label: '15 mins', value: 15},
  {label: '30 mins', value: 30},
  {label: '45 mins', value: 45},
  {label: '60 mins', value: 60},
  {label: '70 mins', value: 70},
])
const loading = ref(false)

const startOption = ref(false)
const endOption = ref(null)
const disableEndOpion = ref(false)
// const taskCategories = ref([])

/*stepper*/
const step = ref(null)
const stepper = ref(null)

const onStepperTransition = () => {
  if (step.value == 1) {
    resetLinkList()
  } else {
    initFlatStart()
    initFlatEnd()
    initStartOfAllSelfLinks()
  }
}

const resetLinkList = () => {
  linkListSorted.value = []
  checkedLinkContents.value = []

  if (!isReschedule.value) {
    linkList.value.forEach((item) => {
      delete item.block
      delete item.color
      delete item.tagId
      delete item.category
      delete item.endActive
      delete item.startActive
      delete item.countdownType
      delete item.countdownNum
      delete item.deadline
    })
  }
  endOption.value = null
  startOption.value = false
  startDate.value = null
  endDate.value = null
  selectedCountdown.value = null
  checkAllSessions.value = false
}

const onSessionDragStart = () => {
  //onAllClicked(false)
  checkedLinkContents.value = []
  hasStartActive.value = false
  hasEndActive.value = false
  checkAllSessions.value = false
  linkListSorted.value.forEach((item) => {
    item.startActive = false
    item.endActive = false
  })
}

const onSessionDragEnd = async (evt) => {
  if (evt.oldIndex === evt.newIndex) return
  const links = getMainLinksByGroup({_id: evt.to.dataset.group})
  const _oldLink = links[evt.oldIndex - 1]
  //if (_oldLink?.imported || _oldLink?.sessionType == 'live') return
  if (_oldLink?.sessionType == 'live') return

  const id1 = _oldLink?._id
  const id2 = links[evt.newIndex - 1]?._id

  const index1 = linkListSorted.value.findIndex((item) => item._id === id1)
  const oldLink = linkListSorted.value.splice(index1, 1)

  oldLink[0]['previous'] = true
  oldLink[0]['countdownType'] = 0
  oldLink[0]['countdownNum'] = null
  oldLink[0]['deadline'] = null
  oldLink[0]['dragging'] = true

  const index2 = linkListSorted.value.findIndex((item) => item._id === id2)

  linkListSorted.value.splice(index2 + (evt.oldIndex < evt.newIndex ? 1 : 0), 0, ...oldLink)

  makeSublinksAlongWithMain(id1)

  if (!isReschedule.value) {
    setStartToPreviousLiveSession()
  }
  await sleep(300)
  oldLink[0]['dragging'] = false
  resetSettings('end')
}

const makeSublinksAlongWithMain = (mainLinkId) => {
  const subLinks = linkListSorted.value.filter((item) => item.parent == mainLinkId)
  subLinks.forEach((item) => {
    item.previous = true
    item.hasPrevious = true
    item.countdownType = 0
    item.countdownNum = null
    item.deadline = null
  })
  const otherLinks = linkListSorted.value.filter((item) => item.parent !== mainLinkId)
  const mainLinkIndex = otherLinks.findIndex((item) => item._id === mainLinkId)
  otherLinks.splice(mainLinkIndex + 1, 0, ...subLinks)
  linkListSorted.value = otherLinks
}

const linkListSorted = ref([]) //for step 2 only

const setStartToPreviousLiveSession = () => {
  linkListSorted.value.forEach((link, index) => {
    let previousLiveLink = getPreviousLiveLink(index)
    if (link.sessionType !== 'live' && (!link.start || link.previous)) {
      if (previousLiveLink) {
        link.previous = true
        link.hasPrevious = true
        link.start = previousLiveLink.end
      } else {
        link.previous = false
        link.start = date.formatDate(new Date(), 'YYYY-MM-DD HH:mm')
      }
    }
  })
}

const getLastLiveLink = (links) => {
  let lastLiveLink = null
  links.forEach((link) => {
    if (link.sessionType == 'live') {
      lastLiveLink = link
    }
  })

  return lastLiveLink
}

const initStartOfAllSelfLinks = () => {
  const allLinks = []
  const mainLinks = []
  let lastLiveLinkOfPreviousGroup = null
  groups.value.forEach((group) => {
    const mainLinksInGroup = linkList.value.filter((item) => item.group == group._id && item.main)
    mainLinksInGroup.forEach((link, index) => {
      if (link.imported || link.scheduled) {
        link.start = date.formatDate(link.start, 'YYYY-MM-DD HH:mm')
        if (link.sessionType == 'live') {
          link.end = date.formatDate(link.end, 'YYYY-MM-DD HH:mm')
        } else {
          link.countdownNum = link.countdown?.down
          link.countdownType = link.countdown?.type
          link.deadline = link.countdown?.deadline ? date.formatDate(new Date(link.countdown.deadline), 'YYYY-MM-DD HH:mm') : null
          delete link.countdown
        }
      } else if (link.sessionType !== 'live') {
        let previousLiveLink = getPreviousLiveLink(index, mainLinksInGroup)
        if (!route.query.start && (previousLiveLink || lastLiveLinkOfPreviousGroup)) {
          link.previous = true
          link.hasPrevious = true
          link.start = previousLiveLink?.end || lastLiveLinkOfPreviousGroup?.end
        } else {
          link.previous = false
          if (route.query.start) {
            link.start = date.formatDate(new Date(route.query.start), 'YYYY-MM-DD HH:mm')
          } else {
            if ((isUnit.value && liveList.value.length) || (!isUnit.value && isLiveUnit.value)) {
              link.start = date.formatDate(new Date(), 'YYYY-MM-DD HH:mm')
            } else {
              link.start = date.formatDate(new Date(date.addToDate(new Date(), {hours: 24})), 'YYYY-MM-DD HH:mm')
            }
          }

          if (route.query.end) {
            link.countdownType = 1
            link.deadline = date.formatDate(route.query.end, 'YYYY-MM-DD HH:mm')
          }
        }
      }
    })

    mainLinks.push(...mainLinksInGroup)
    lastLiveLinkOfPreviousGroup = getLastLiveLink(mainLinksInGroup)
  })

  mainLinks.forEach((link) => {
    allLinks.push(...setSublinks(link, 'Preparations'))
    allLinks.push(link)
    allLinks.push(...setSublinks(link, 'After'))
  })

  linkListSorted.value = allLinks
}

const setSublinks = (link, type) => {
  const sublinks = linkList.value.filter((item) => item.parent == link._id && item.pidGroupName.indexOf(type) !== -1)
  sublinks.forEach((item) => {
    if (item.imported || item.scheduled) {
      item.start = date.formatDate(item.start, 'YYYY-MM-DD HH:mm')
      item.countdownNum = item.countdown?.down
      item.countdownType = item.countdown?.type
      item.deadline = item.countdown?.deadline ? date.formatDate(new Date(item.countdown.deadline), 'YYYY-MM-DD HH:mm') : null
      delete item.countdown
      if (item.start == link.end) {
        item.previous = true
        if (link.sessionType == 'live') {
          item.hasPrevious = true
        }
      }
    } else {
      item.start = link.start
      item.previous = link.previous
      if (link.sessionType == 'live' && type !== 'Preparations') {
        item.previous = true
        item.hasPrevious = true
        item.start = link.end
      }
    }
  })

  return sublinks
}

/*flatpickr*/

let flatStartInstance = null
let flatEndInstance = null

const initFlatStart = (reinit) => {
  let minDate = new Date()

  if ((isUnit.value && !liveList.value.length) || (!isUnit.value && !isLiveUnit.value)) {
    //minDate = new Date(date.addToDate(minDate, {hours: 24}))
  }

  if (flatStartInstance) {
    try {
      flatStartInstance.destroy()
    } catch (e) {
      console.log(e)
    }
    flatStartInstance = null
  }

  const config = {
    enableTime: true,
    time_24hr: true,
    minuteIncrement: 1,
    dateFormat: 'Y-m-d H:i',
    defaultDate: startDate.value,
    minDate,
    onChange: () => {},
    onReady: () => {},
    onClose: () => {
      setTimeout(() => {
        initFlatStart(true)
      }, 500)
    },
  }
  if (new Date(startDate.value) > new Date(endDate.value)) {
    endDate.value = startDate.value
  }
  initFlatEnd(true)

  if (!reinit) {
    /*
    if (props.modelValue.countdown?.deadline) {
      deadline.value = flatpickr.formatDate(new Date(props.modelValue.countdown.deadline), 'Y-m-d H:i')
      config.defaultDate = new Date(props.modelValue.countdown.deadline)
      if (config.minDate.getTime() >= config.defaultDate.getTime()) {
        config.minDate = config.defaultDate
      }
    } else {
      //config.defaultDate = startDate.value
    }
    */
  }
  flatStartInstance = flatpickr('#flat-start', config)
}

const initFlatEnd = (reinit) => {
  if (flatEndInstance) {
    try {
      flatEndInstance.destroy()
    } catch (e) {
      console.log(e)
    }
    flatEndInstance = null
  }

  const config = {
    enableTime: true,
    time_24hr: true,
    minuteIncrement: 1,
    dateFormat: 'Y-m-d H:i',
    defaultDate: endDate.value,
    minDate: startDate.value || _startDate.value,
    onChange: () => {},
    onReady: () => {},
    onClose: () => {
      // setTimeout(() => {
      //   //initFlatEnd(true)
      // }, 500)
    },
  }
  if (!reinit) {
    /*
    if (props.modelValue.countdown?.deadline) {
      deadline.value = flatpickr.formatDate(new Date(props.modelValue.countdown.deadline), 'Y-m-d H:i')
      config.defaultDate = new Date(props.modelValue.countdown.deadline)
      if (config.minDate.getTime() >= config.defaultDate.getTime()) {
        config.minDate = config.defaultDate
      }
    } else {
      //config.defaultDate = new Date()
    }
    */
  }
  //flatEndInstance = flatpickr('.flat-end input.q-field__native', config)
  flatEndInstance = flatpickr('#flat-end', config)
}

/*
  lifecycle
*/

onMounted(async () => {
  loading.value = true
  Aincludes('/v2/plugin/flatpickr.min.css', 'css')
  await Aincludes('/v2/plugin/flatpickr.min.js', 'js')
  await sleep(500)
  if (pub.user.schoolInfo?._id) {
    isPersonal.value = false
  }
  let authDoc
  if (route.params.authId) {
    const {unitId} = route.query
    authDoc = await App.service('service-auth').get(route.params.authId)
    one.value = unitId ? authDoc.linkSnapshot[unitId] : authDoc.unitSnapshot
    isLiveUnit.value = one.value.sessionType == 'live'
  }
  if (route.params.unitId) {
    one.value = await getOneById(route.params.unitId)
    isLiveUnit.value = one.value.sessionType == 'live'
  }

  if (route.params.sessionId) {
    targetSessionId.value = route.params.sessionId
    targetSession.value = await App.service('session').get({_id: targetSessionId.value})

    if (route.path.includes('/reschedule/')) {
      isReschedule.value = true
      isLiveUnit.value = targetSession.value.sessionType == 'live'
      if (targetSession.value.zoom?.id) {
        disableZOOM.value = true
      }
    } else {
      isImportToSession.value = true
      targetSessionGroup.value = route.params.groupId && route.params.groupId !== '0' ? route.params.groupId : 0
      disableZOOM.value = true
      disableClass.value = true
      disableGrade.value = true
      disableStudents.value = true
      disableMin.value = true
      disableMax.value = true
    }
    disableParticipant.value = true
    const attributes = contentsType[targetSession.value.type]

    isTask.value = attributes.session
    isUnit.value = attributes.course

    if (attributes.educator) {
      isEducator.value = true
      if (attributes.public) {
        isPublic.value = true
        form.value.participant = 'public'
      } else {
        form.value.selectedTeachersOrGrades = targetSession.value.students
      }
    } else {
      isEducator.value = false
      if (attributes.public) {
        isPublic.value = true
        form.value.participant = 'public'
        if (targetSession.value.grades) {
          form.value.selectedTeachersOrGrades = targetSession.value.grades
        }
      } else {
        form.value.selectedClass = {}
        form.value.selectedClass.classId = targetSession.value.classId
        form.value.selectedClass.label = targetSession.value.className
        form.value.selectedStudents = targetSession.value.students
      }
    }

    if (isPublic.value) {
      form.value.group = targetSession.value.discount.size
      if (targetSession.value.discount?.price) {
        form.value.isPaid = true
        form.value.price = targetSession.value.discount.price / 100
        if (targetSession.value.discount?.val) {
          form.value.isGroup = true
          form.value.discount = targetSession.value.discount.val
        }
      }

      if (targetSession.value.regMax) {
        form.value.isMax = true
        form.value.regMax = targetSession.value.regMax
      }
    }

    await getRelateSessions()
  } else {
    const attributes = contentsType[one.value.mode]

    isVideo.value = attributes.video
    isTask.value = attributes.task
    isUnit.value = attributes.unit
    isTool.value = attributes.tool

    if (['#educators', '#students'].includes(route.hash) || one.value.service?.participants == 'educators') {
      isEducator.value = true
      if (route.hash == '#students') {
        isEducator.value = false
        isImportFromMyServicePage.value = true
      }
      isPublic.value = true
      form.value.participant = 'public'
    }

    groups.value = one.value.linkGroup
    await getRelateList(authDoc.linkSnapshot)
  }

  /*
  if (isPublic.value) {
    defaultOpenZoom.value = true
  } else {
    canCloseZoom.value = true
  }*/

  getTeacherOrGradeDetails()
  await getClassesOptions()
  onParticipantClick(form.value.participant)

  if (isImportToSession.value && !isLiveUnit.value) {
    step.value = 2
  } else {
    step.value = 1
  }

  if ($q.screen.lt.sm) {
    step.value = 0
  }

  if (step.value == 1) {
    //initFlatDeadline()
  } else {
    initFlatStart()
    initFlatEnd()
  }

  getServiceAuth()
  loading.value = false
})

const getRelateSessions = async () => {
  const _ids = []
  targetSession.value.childs.forEach((item) => {
    _ids.push(item._id)
  })

  const _sessions = await App.service('session').find({query: {_id: {$in: _ids}, status: {$exists: true}, $limit: 1000}})
  const sessionsData = _sessions.data.sort((a, b) => {
    return new Date(a.start) - new Date(b.start)
  })

  const targetSessionChilds = targetSession.value.childs.reduce(function (r, a) {
    if (a.group) {
      r[a.group] = r[a.group] || []
      r[a.group].push(a)
    }
    return r
  }, Object.create(null))

  Object.values(targetSessionChilds).forEach((group) => {
    const temp = []
    group.forEach((item) => {
      const itemDetail = sessionsData.find((i) => i._id == item._id)
      if (itemDetail?.pid == targetSessionId.value) {
        const _item = {...item, ...itemDetail, ...{main: true, scheduled: isReschedule.value, imported: isImportToSession.value}}
        /*
        if (isReschedule.value) {
          if (_item.sessionType == 'live') {
            _item.start = date.formatDate(new Date(_item.start), 'YYYY-MM-DD HH:mm')
            _item.end = date.formatDate(new Date(_item.end), 'YYYY-MM-DD HH:mm')
          } else {
            _item.countdownNum = itemDetail.countdown.down
            _item.countdownType = itemDetail.countdown.type
            _item.deadline = itemDetail.countdown.deadline ? date.formatDate(new Date(itemDetail.countdown.deadline), 'YYYY-MM-DD HH:mm') : null
          }
        }*/
        temp.push(_item)
      }
      const childs = sessionsData.filter((c) => c.pid == item._id)
      childs.forEach((cld) => {
        cld.group = item.group
        cld.groupName = item.groupName
        cld.parent = item._id
        cld.imported = isImportToSession.value
        cld.scheduled = isReschedule.value
        temp.push(cld)
      })
    })
    groupSessions.value.push(temp)
  })

  const _groups = []
  const _allSessions = []
  groupSessions.value.forEach((group) => {
    groups.value.push({_id: group[0]?.group, name: group[0]?.groupName})
    const _sessions = []
    const mainSessions = group
      .filter((item) => item.pid == targetSessionId.value)
      .sort((a, b) => {
        return new Date(a.start) - new Date(b.start)
      })
    mainSessions.forEach((session) => {
      _sessions.push(session)
      _allSessions.push(session)
      const subSessions = group.filter((item) => item.pid == session._id)
      _sessions.push(...subSessions)
      _allSessions.push(...subSessions)
    })
    _groups.push(_sessions)
  })

  groupSessions.value = _groups
  linkList.value = _allSessions

  if (!isUnit.value) {
    groups.value = targetSession.value.task.linkGroup
    groups.value.splice(1, 0, {_id: 'default', name: 'Current'})
    let bgcolor = colors[0 % colors.length]
    const _target = {...targetSession.value, bgcolor, main: true, scheduled: isReschedule.value, imported: isImportToSession.value, group: 'default'}

    if (targetSession.value.sessionType == 'live' && isReschedule.value) {
      _target.start = date.formatDate(new Date(targetSession.value.start), 'YYYY-MM-DD HH:mm')
      _target.end = date.formatDate(new Date(targetSession.value.end), 'YYYY-MM-DD HH:mm')
    }
    linkList.value.unshift(_target)
  }

  const importedLiveSessions = linkList.value.filter((item) => item.sessionType == 'live')

  importedLiveSessions.forEach((link, index) => {
    let bgcolor = colors[index % colors.length]
    const _link = {...link, ...{bgcolor, readonly: isImportToSession.value}}
    calendarSessions.value.push(_link)
    liveList.value.push(_link)
  })

  if (isImportToSession.value) {
    const bgcolor = colors[(importedLiveSessions.length + 1) % colors.length]
    const _one = {...one.value, ...{bgcolor, start: 0, end: 0, main: true, group: targetSessionGroup.value, scheduled: false}}

    if (!targetSessionGroup.value) {
      _one.group = groupSessions.value[0][0]['group']
    }

    linkList.value.push(_one)
    if (isLiveUnit.value) {
      liveList.value.push(_one)
    }
  }
}

/*
  methods
*/
const onPackageTipClick = () => {
  if (noPackageTip.value) {
    LocalStorage.set(packageTipKey, true)
  }
  slideToS3()
}

const onResize = (size) => {
  if (size.width >= 600 && step.value == 0) {
    step.value = 1
    leftDrawerOpen.value = true
  }

  if (size.width < 600 && step.value == 1) {
    //step.value = 0
    //leftDrawerOpen.value = false
  }
}

const getType = (item) => {
  const isTaskMode = ['task', 'pdTask'].includes(item.mode)
  const isPDTaskMode = item.mode == 'pdTask'
  const isPDUnitMode = item.mode == 'pdUnit'
  const isToolMode = item.mode == 'tool'
  const isVideoMode = item.mode == 'video'

  if (isVideoMode) {
    return 'videoSession'
  }
  let _type = null

  if (isEducator.value) {
    if (isPersonal.value) {
      _type = isTaskMode ? 'workshop' : isToolMode ? 'teacherTool' : 'pdCourses'
    } else {
      if (isPublic.value) {
        _type = isTaskMode ? 'pdSchoolTeacherWorkshop' : isToolMode ? 'teacherToolSchool' : 'pdSchoolTeacherCourses'
      } else {
        // my colleague
        _type = isTaskMode ? 'pdSchoolWorkshop' : isToolMode ? 'teacherToolSelect' : 'pdSchoolCourses'
      }
    }
  } else {
    if (isPublic.value) {
      if (isPersonal.value) {
        if (isTaskMode) {
          _type = isPDTaskMode ? 'studentWorkshop' : 'taskWorkshop'
        } else {
          _type = isPDUnitMode ? 'studentCourses' : isToolMode ? 'studentTool' : 'unitCourses'
        }
      } else {
        if (isTaskMode) {
          _type = isPDTaskMode ? 'pdSchoolStudentWorkshop' : 'taskSchoolWorkshop'
        } else {
          _type = isPDUnitMode ? 'pdSchoolStudentCourses' : isToolMode ? 'studentToolGrade' : 'unitSchoolCourses'
        }
      }
    } else {
      if (isTaskMode) {
        _type = isPDTaskMode ? 'pdClassSession' : 'session'
      } else {
        _type = isPDUnitMode ? 'pdClassCourses' : isToolMode ? 'studentToolSelect' : 'courses'
      }
    }
  }

  return _type
}

const onCategorySettingConfirm = (selectedCategory) => {
  checkedLinkContents.value.forEach((item) => {
    item.category = selectedCategory.name
    item.color = selectedCategory.color
    item.tagId = selectedCategory._id
  })
}

const onBlockSetting = (block) => {
  checkedLinkContents.value.forEach((item) => {
    item.block = block
  })
}

const onParticipantClick = (type) => {
  if (!liveList.value.length) {
    //temporary #4123
    type = 'class'
  }
  form.value.participant = type
  if (type == 'public') {
    if (liveList.value.length) {
      //#5246
      defaultOpenZoom.value = true
      canCloseZoom.value = false
      form.value.zoom.checked = true
    }
    isPublic.value = true
    form.value.selectedPeople = form.value.selectedTeachersOrGrades
  } else {
    if (liveList.value.length) {
      //#5246
      defaultOpenZoom.value = true
      canCloseZoom.value = true
    }
    isPublic.value = false

    if (isEducator.value) {
      form.value.selectedPeople = form.value.selectedTeachersOrGrades
    } else {
      form.value.selectedPeople = form.value.selectedStudents
    }
  }

  if (!liveList.value.length) {
    //#5246
    defaultOpenZoom.value = false
    canCloseZoom.value = true
  }

  if (!loading.value) {
    getScheduledSessions()
  }
}

const onBackClick = () => {
  if (step.value == 2) {
    step.value = 1
  } else if (step.value == 1 && $q.screen.lt.sm) {
    step.value = 0
  } else {
    router.go(-1)
  }
}

const onNextBtnClick = () => {
  if (form.value.selectedClass && !isEducator.value && !isPublic.value) {
    updateClassDetails(form.value.selectedClass, true)
  }

  if (step.value != 0 && !isImportToSession.value && isPublic.value && ((isUnit.value && liveList.value.length) || (!isUnit.value && isLiveUnit.value))) {
    allLiveStart.value = getStartOrEndOfAll('start', true)
    if (new Date(allLiveStart.value) < new Date(form.value.regDate)) {
      form.value.regDate = null
    }
    showDeadline.value = true
  } else if ((step.value == 0 && !form.value.hasError) || readyToNext(true)) {
    stepper.value.next()
  }
}

const getUniqueSubjects = (item) => {
  const allSubjects = []
  item.subjects?.forEach((s) => {
    allSubjects.push({label: s.label, value: s.value})
  })
  item.outlineSubjects?.forEach((value) => {
    allSubjects.push({label: value, value})
  })
  const uniqueSubjects = allSubjects.reduce((unique, item) => {
    const found = unique.find((obj) => obj.value === item.value)
    if (!found) {
      unique.push(item)
    }
    return unique
  }, [])

  return uniqueSubjects
}

const createOrPatchSublinks = async (link, pid) => {
  const query = {}

  if (!isReschedule.value) {
    query.subjects = getUniqueSubjects(link)
    query.cid = link.id ? link.id : link._id
    query.pid = pid
    query.name = link.name
    query.block = link.block
    query.unitType = link.type
    query.image = link.cover

    if (link.pidGroup) {
      query.pidGroup = link.pidGroup
      query.pidGroupName = link.pidGroupName
    }

    query.type = getType(link)
    if (link.mode == 'tool') {
      query.type = 'toolSession'
    }
    query.status = 'student'
  }
  query.countdown = {}
  query.countdown.type = link.countdownType
  query.countdown.down = link.countdownNum
  query.countdown.deadline = link.deadline ? new Date(link.deadline).toISOString() : null
  query.start = new Date(link.start).toISOString()
  query.school = pub.user.schoolInfo?._id
  if (isPublic.value) {
    if (!isPersonal.value && !isEducator.value) {
      query.grades = form.value.selectedPeople
    }
  } else {
    query.students = form.value.selectedPeople
    if (!isEducator.value) {
      query.classId = form.value.selectedClass.classId
      query.className = form.value.selectedClass.label
      if (form.value.selectedPeople.length === 0) {
        query.guest = true
      }
    }
  }

  if (link.category) {
    query.category = link.category
  }

  if (link.color) {
    query.color = link.color
  }

  let sessionDoc = null
  if (isReschedule.value) {
    sessionDoc = await App.service('session').patch(link._id, query)
  } else {
    query.premium = false
    query.promotion = false
    if (isPersonal.value) {
      query.personal = true
    }
    sessionDoc = await App.service('session').create(query)
  }

  return {
    _id: sessionDoc._id,
    sid: sessionDoc.sid,
    cid: sessionDoc.cid,
    mode: link.mode,
    group: link.group,
    sessionType: link.sessionType,
    groupName: getGroupNameById(link.group),
  }
}

const SubjectOptions = inject('SubjectOptions')
const subjectOptions = ref([])
SubjectOptions.forEach((item) => {
  subjectOptions.value.push({label: item, value: item})
})
const launching = ref(false)
const onLaunchBtnClick = () => {
  let message = null
  let key = null
  if (!isPersonal.value) {
    if (!isEducator.value) {
      message = 'The sessions will be launched to all students of your organization.'
      key = 'SCHEDULETIP_SCHOOL_NOT_EDUCATOR'
    } else {
      message = 'The sessions will be launched to all educators of your organization. '
      key = 'SCHEDULETIP_SCHOOL_EDUCATOR'
    }
  } else {
    if (!isEducator.value) {
      message = 'The sessions will be launched to all student users of Classcipe. '
      key = 'SCHEDULETIP_STUDENT_NOT_EDUCATOR'
    } else {
      message = 'The sessions will be launched to all educator users of Classcipe. '
      key = 'SCHEDULETIP_STUDENT_EDUCATOR'
    }
  }

  const scheduleTip = LocalStorage.getItem(key)
  if (isPublic.value && !scheduleTip) {
    $q.dialog({
      title: 'Tips',
      message,
      options: {
        type: 'radio',
        model: '',
        items: [{label: 'Please do not show this again', value: 'ok'}],
      },
      cancel: true,
      persistent: true,
    }).onOk((data) => {
      LocalStorage.set(key, data)
      recommendServce()
    })
  } else {
    recommendServce()
  }
}

const onPurposeConfirm = () => {
  if (purpose.value.value == 'promotion') {
    isPromotion.value = true
  } else {
    isPromotion.value = false
  }

  $q.dialog({
    component: RecommendDialog,
    componentProps: {
      curriculum: one.value.curriculum,
      grades: one.value.grades,
      serviceType: one.value.service?.type?.[0] || 'academic',
      promotion: isPromotion.value,
      participants: one.value.service?.participants ?? '',
      liveSessions: liveList.value.length,
    },
  })
    .onOk((obj) => {
      if (obj.skip) {
        slideToS3()
      } else {
        if (obj.package?._id) {
          const pack = obj.package

          const calDiscount = calServiceDiscount(pack?.discountConfig)
          const calPackage = calPremiumMentor({
            servicePack: pack,
            count: liveList.value.length,
            discount: calDiscount,
          })

          // const reduction = pack?.discount?.find((item) => liveList.value.length >= item.count)
          //const price = pack.price * (reduction ? 1 - reduction.discount / 100 : 1)

          servicePack._id = pack._id
          servicePack.price = calPackage?.price
          servicePack.times = liveList.value.length
        }

        if (obj.session?._id) {
          promotionSession.value = obj.session._id
        }

        const packageTip = LocalStorage.getItem(packageTipKey)
        if (isPromotion.value && !packageTip) {
          packageAlert.value = true
        } else {
          slideToS3()
        }
      }
    })
    .onCancel(() => {
      purposeOptions.value.forEach((option) => {
        delete option.active
      })
      purpose.value = {}
    })
}

const recommendServce = () => {
  if (isPersonal.value && isPublic.value && isPremium.value) {
    if (liveList.value.length == 1) {
      purposeAlert.value = true
    } else {
      purpose.value = {}
      purpose.value.value = 'paid'
      onPurposeConfirm()
    }
  } else {
    slideToS3()
  }
}

const slideToS3 = async () => {
  /*
  const slideIds = linkListSorted.value.filter((e) => e.sid && !e.sid.includes(':')).map((e) => e.sid)
  const dialog = $q.dialog({
    message: `Downloading slides... ${Math.ceil(skip / slideIds.length) || 10}%`,
    progress: true,
    persistent: true,
    ok: false,
  })
  for (let i = 0; i < slideIds.length; i++) {
    if (i >= skip) {
      const rs = await App.service('slides').get('slideToS3', {query: {id: slideIds[i].split(':').pop(), uid: pub.user._id}})
      dialog.update({
        message: `Downloading slides... ${Math.ceil(i / slideIds.length)}%`,
      })
      if (!rs?._id) return googleAuthExt().then(() => slideToS3(i))
    }
  }

  dialog.hide()
  */
  doLaunch()
}

const doLaunch = async () => {
  const dialog = $q.dialog({
    message: 'Launching... 10%',
    progress: true, // we enable default settings
    persistent: true, // we want the user to not be able to close it
    ok: false, // we want the user to not be able to close it
  })

  let doneCount = 0

  //if (isImportToSession.value) {
  //  doneCount = linkListSorted.value.length - 1
  //}

  let query = {}

  launching.value = true
  const childs = []
  let lastSessionId = null

  for (let i = 0; i < linkListSorted.value.length; i++) {
    const link = linkListSorted.value[i]
    //if (!link.main || link.imported) continue
    if (!link.main) continue
    query = {}

    if (!isReschedule.value && !link.imported) {
      query.id = link.sid
      query.cid = link.id ? link.id : link._id
      query.unitType = link.type
      query.sessionType = link.sessionType
      if (link.mode == 'tool') {
        query.sessionType = 'student'
      }
      query.subjects = getUniqueSubjects(link)

      if (link.pidGroup) {
        query.pidGroup = link.pidGroup
        query.pidGroupName = link.pidGroupName
      }

      if (form.value.zoom.checked && link.mode !== 'tool') {
        query.zoom = form.value.zoom
        delete query.zoom.invalid
      } else {
        delete query.zoom
      }

      query.name = link.name
      query.image = link.cover
    }

    query.block = !!link.block
    if (link.category) {
      query.category = link.category
    }
    if (link.color) {
      query.color = link.color
    }

    if (link.imported) {
      //do nothing
    } else if (isPublic.value) {
      //Public
      query.discount = {}
      query.discount.size = form.value.group

      if (form.value.isPaid) {
        query.discount.price = form.value.price * 100
        if (form.value.isGroup) {
          query.discount.val = form.value.discount
        }
      }

      if (form.value.isMax) {
        query.regMax = form.value.regMax
      }

      if (!isPersonal.value && !isEducator.value) {
        query.grades = form.value.selectedPeople
      }

      if (!isReschedule.value) {
        if (form.value.regDate) {
          query.regDate = new Date(form.value.regDate).toISOString()
        } else {
          query.regDate = getStartOrEndOfAll('start')
        }
      }
    } else {
      //My class or My colleague
      query.students = form.value.selectedPeople
      if (!isEducator.value && !isReschedule.value) {
        query.classId = form.value.selectedClass.classId
        query.className = form.value.selectedClass.label
        if (form.value.selectedPeople.length === 0) {
          query.guest = true
        }
      }
    }

    if (!isReschedule.value && !link.imported) {
      query.type = getType(link)
      if (link.mode == 'tool' && one.value._id !== link._id) {
        query.type = 'toolSession'
      }
      query.school = pub.user.schoolInfo?._id
    }

    query.start = new Date(link.start).toISOString()

    if (link.sessionType != 'live') {
      query.countdown = {}
      query.status = 'student'
      query.countdown.type = link.countdownType
      query.countdown.down = link.countdownNum
      query.countdown.deadline = link.deadline ? new Date(link.deadline).toISOString() : null
    } else {
      query.status = 'live'
      query.end = new Date(link.end).toISOString()
    }

    if (isImportToSession.value && !link.imported) {
      query.pid = targetSessionId.value
    }

    let sessionDoc = null
    if (isReschedule.value || (isImportToSession.value && link.imported)) {
      sessionDoc = await App.service('session').patch(link._id, query)
    } else {
      if (isPersonal.value && isPublic.value && isPremium.value) {
        query.premium = true
        query.promotion = isPromotion.value
        if (servicePack?._id) {
          query.servicePack = servicePack
        }
        if (promotionSession.value) {
          query.promotionSession = promotionSession.value
        }
      } else {
        query.premium = false
        query.promotion = false
      }
      if (isPersonal.value) {
        query.personal = true
      }
      sessionDoc = await App.service('session').create(query)
    }

    if (query.zoom && !sessionDoc?.zoom?.id) {
      console.error(query, sessionDoc?.zoom, '<=======zoom error================')
      zoomConnected.value = false
    }
    if (!sessionDoc?._id) {
      console.error(sessionDoc, '<======create session failed')
    }

    doneCount += 1
    dialog.update({
      message: `Launching... ${Math.ceil((100 * doneCount) / linkListSorted.value.length)}%`,
    })

    if (isTask.value && link._id == one.value?._id) {
      lastSessionId = sessionDoc._id
    }

    if (isUnit.value && sessionDoc?._id && !link.imported) {
      childs.push({
        _id: sessionDoc._id,
        sid: sessionDoc.sid,
        cid: sessionDoc.cid,
        mode: link.mode,
        group: link.group,
        sessionType: link.sessionType,
        groupName: getGroupNameById(link.group),
      })
      if (!isImportToSession.value) {
        const subLinks = linkListSorted.value.filter((item) => item.parent == link._id)
        for (let j = 0; j < subLinks.length; j++) {
          const sublink = subLinks[j]
          const obj = await createOrPatchSublinks(sublink, sessionDoc._id)
          childs.push(obj)
          doneCount += 1
          dialog.update({
            message: `Launching... ${Math.ceil((100 * doneCount) / linkListSorted.value.length)}%`,
          })
        }
      }
    }

    if (isTask.value && ((!isImportToSession.value && link._id != one.value?._id) || (isImportToSession.value && !link.imported)) && !isReschedule.value) {
      childs.push({
        _id: sessionDoc._id,
        sid: sessionDoc.sid,
        cid: sessionDoc.cid,
        mode: link.mode,
        group: link.group,
        sessionType: link.sessionType,
        groupName: getGroupNameById(link.group),
      })
    }
  }

  if (isImportToSession.value) {
    if (isUnit.value) {
      const post = {
        sessionType: liveList.value.length ? 'live' : 'student',
        childs: [...targetSession.value.childs, ...childs],
      }

      if (new Date() < new Date(targetSession.value.start)) {
        //DO NOT UPDATE start AND end if the COURSE is ONGOING or ENDED #3862
        //post.start = getStartOrEndOfAll('start', liveList.value.length ? true : false)
        //post.end = getStartOrEndOfAll('end', liveList.value.length ? true : false)
      }
      await App.service('session').patch(targetSessionId.value, post)
    } else {
      await App.service('session').patch(targetSessionId.value, {
        childs: [...targetSession.value.childs, ...childs],
      })
    }
  }

  if (isTask.value && lastSessionId && !isImportToSession.value && !isReschedule.value) {
    await App.service('session').patch(lastSessionId, {childs})
    for (let i = 0; i < childs.length; i++) {
      await App.service('session').patch(childs[i]['_id'], {pid: lastSessionId})
    }
  }

  if (isUnit.value && !isImportToSession.value) {
    query = {premium: false, promotion: false}
    if (!isReschedule.value) {
      query.subjects = getUniqueSubjects(one.value)
      query.cid = one.value._id
      query.childs = childs
      query.name = one.value.name
      query.image = one.value.cover
      query.unitType = one.value.type
      query.school = pub.user.schoolInfo?._id
      query.sessionType = liveList.value.length ? 'live' : 'student'
      query.status = liveList.value.length ? 'live' : 'student'
    }

    if (isPersonal.value && isPublic.value && isPremium.value) {
      query.premium = true
      query.promotion = isPromotion.value
      if (servicePack?._id) {
        query.servicePack = servicePack
      }
      if (promotionSession.value) {
        query.promotionSession = promotionSession.value
      }
    }

    if (isPublic.value) {
      query.discount = {}
      query.discount.size = form.value.group
      if (form.value.isPaid) {
        query.discount.price = form.value.price * 100
        if (form.value.isGroup) {
          query.discount.val = form.value.discount
        }
      }

      if (form.value.isMax) {
        query.regMax = form.value.regMax
      }

      if (!isPersonal.value && !isEducator.value) {
        query.grades = form.value.selectedPeople
      }

      if (form.value.regDate) {
        query.regDate = new Date(form.value.regDate).toISOString()
      } else {
        query.regDate = getStartOrEndOfAll('start', false)
      }
    } else {
      query.students = form.value.selectedPeople
      if (!isEducator.value && !isReschedule.value) {
        query.classId = form.value.selectedClass.classId
        query.className = form.value.selectedClass.label
      }
    }

    if (!isReschedule.value) {
      query.type = getType(one.value)
      if (form.value.zoom.checked && one.value.mode !== 'tool') {
        query.zoom = form.value.zoom
        delete query.zoom.invalid
      } else {
        delete query.zoom
      }
    }
    query.start = getStartOrEndOfAll('start', liveList.value.length ? true : false)
    query.end = getStartOrEndOfAll('end', liveList.value.length ? true : false)
    if (!query.end) {
      delete query.end
    }
    let rs = null
    if (isReschedule.value) {
      rs = await App.service('session').patch(targetSessionId.value, query)
    } else {
      if (isPersonal.value) {
        query.personal = true
      }
      rs = await App.service('session').create(query)
    }

    if (query.zoom && rs && !rs?.zoom?.id) {
      console.error(query, rs?.zoom, '<=======zoom error================')
      zoomConnected.value = false
    }
  }

  dialog.hide()
  launching.value = false

  let href = ''
  let status = 'scheduled'
  if (new Date(query.start) < new Date()) {
    status = 'ongoing'
  }

  const range = getRange(query.start)
  if (!isEducator.value && !isPublic.value) {
    href = `/home/<USER>
      isUnit.value ? query.sessionType : isLiveUnit.value ? 'live' : 'student'
    }&type=${isUnit.value ? 'courses' : 'sessions'}&by=me&range=${range}`
  } else {
    href = `/home/<USER>'live' : 'student'}&type=${
      isUnit.value ? 'courses' : 'sessions'
    }&part=${isEducator.value ? 'educators' : 'students'}&range=${range}`
  }

  if (isImportToSession.value || isReschedule.value) {
    //if (targetSessionGroup.value) {
    href = `/detail/${isUnit.value ? 'course' : 'session'}/${targetSessionId.value}#${targetSessionGroup.value}`
    //} else {
    // href = decodeURIComponent(route.query.back)
    //}
  }

  if (form.value.zoom.checked && !zoomConnected.value) {
    zoomAlert.value = true
    zoomAlertHref.value = href
  } else {
    routerReplace(href)
  }
}

const routerReplace = (href) => {
  router.replace(href)
}

const getRange = (start) => {
  const newDate = new Date(start)
  const startDate = date.startOfDate(newDate, 'year')
  const endDate = date.endOfDate(newDate, 'year')
  return `${date.formatDate(new Date(startDate), 'MM/DD/YYYY')}~${date.formatDate(new Date(endDate), 'MM/DD/YYYY')}`
}

const getStartOrEndOfAll = (startOrEnd, excludeSelf) => {
  let target = linkListSorted.value
  if (excludeSelf) {
    target = liveList.value
  }
  const sorted = target.toSorted((a, b) => {
    if (startOrEnd == 'start') {
      return new Date(a?.start) - new Date(b?.start)
    } else {
      return new Date(b?.end || b?.deadline || 0) - new Date(a?.end || a?.deadline || 0)
    }
  })

  const firstItem = sorted?.[0]
  if (startOrEnd == 'end' && firstItem.deadline) {
    return new Date(firstItem.deadline).toISOString()
  } else if (firstItem?.[startOrEnd]) {
    return new Date(firstItem[startOrEnd]).toISOString()
  } else {
    return null
  }
}

const noneScheduled = () => {
  let none = true
  liveList.value.forEach((link) => {
    if (link.scheduled || link.imported) {
      none = false
    }
  })

  return none
}

const readyToNext = (showMsg) => {
  let ready = true

  if (step.value != 0) {
    liveList.value.forEach((link) => {
      if (!link.scheduled && !link.imported) {
        ready = false
      }
    })
  }

  //todo
  if (
    (!isEducator.value && isPublic.value && !isPersonal.value && !form.value.selectedTeachersOrGrades?.length) ||
    (!isEducator.value && !isPublic.value && !form.value.selectedClass) ||
    (isEducator.value && !isPublic.value && !isPersonal.value && !form.value.selectedTeachersOrGrades?.length)
  ) {
    if (showMsg) {
      $q.notify({type: 'negative', message: !form.value.selectedClass ? 'No class selected' : isPublic.value ? 'No grade selected' : 'No colleague selected'})
    }
    ready = false
  }

  if (form.value.hasError) {
    ready = false
  }

  return ready
}

const readyToLaunch = () => {
  let ready = true
  linkList.value.forEach((link) => {
    if (link.sessionType == 'live' && !link.scheduled && !link.imported) {
      ready = false
    }

    if (link.sessionType !== 'live' && !link.start) {
      ready = false
    }
  })

  return ready
}

const onClassChanged = (val) => {
  if (form.value.selectedClass && val) {
    linkList.value.forEach((item) => {
      item.scheduled = false
    })
    if (!isImportToSession.value) {
      calendarSessions.value = []
    }
  }

  updateClassDetails(val)

  if (!isImportToSession.value) {
    form.value.selectedClass = val
    form.value.selectedStudents = []
    form.value.selectedPeople = []
    classDetails.value.forEach((people) => {
      form.value.selectedStudents.push(people.uid)
      form.value.selectedPeople.push(people.uid)
    })
  }
  if (!loading.value) {
    getScheduledSessions()
  }
}

const updateClassDetails = async (val, refresh) => {
  if (refresh) {
    await getStudentsList(true)
  }
  switch (val.type) {
    case 'subject':
      const enrollClassIds = val?.enroll?.classes || []
      classDetails.value = studentsList.value.filter((student) => {
        const hasAnyClassId = enrollClassIds.some((classId) => student.class.includes(classId))
        return student.uid && student.del === false && student.status == 2 && hasAnyClassId
      })
      break
    case 'standard':
      classDetails.value = studentsList.value.filter(
        (student) => student.uid && student.del === false && student.status == 2 && student.class.includes(val.classId)
      )
      break
    default:
      break
  }

  if (refresh) {
    const filtered = form.value.selectedStudents.filter((e) => classDetails.value.some((c) => e == c.uid))
    form.value.selectedStudents = filtered
    form.value.selectedPeople = filtered
  }
}

const getTeacherOrGradeDetails = () => {
  if (isEducator.value) {
    teacherOrGradeDetails.value = teachersList.value.filter((item) => item.uid && !item.del && item.uid !== pub.user._id)
  } else {
    teacherOrGradeDetails.value = gradeList.value.filter((item) => !item.del)
  }

  if (!isImportToSession.value) {
    teacherOrGradeDetails.value.forEach((item) => {
      form.value.selectedTeachersOrGrades.push(item.uid || item.value)
    })
  }
}

const hasPreviousLiveSession = computed(() => {
  let has = false
  if (checkedLinkContents.value.length) {
    has = true
    checkedLinkContents.value.forEach((item) => {
      if (!item.hasPrevious) {
        has = false
      }
    })
  }
  return has
})

const getGroupNameById = (id) => {
  //const group = one.value.linkGroup.find((item) => item._id == id)
  const group = groups.value.find((item) => item._id == id)
  return group?.name
}

const liveGroup = ref([])
const getRelateList = async (linkSnapshot) => {
  const rs = await relateLinkList({rid: route.params.unitId})
  one.value.link.forEach((link, index) => {
    let bgcolor = colors[index % colors.length]
    let meta = linkSnapshot[link.id]
    linkList.value[index] = {
      ...meta,
      ...{
        _id: link._id,
        bgcolor,
        start: 0,
        end: 0,
        scheduled: false,
        group: link.group,
        pidGroup: link.group,
        pidGroupName: getGroupNameById(link.group),
        id: link.id,
      },
    }
  })

  let reorderedLinkList = []

  one.value.linkGroup.forEach((group) => {
    reorderedLinkList = [...reorderedLinkList, ...getAllLinksByGroup(group)]
  })

  linkList.value = reorderedLinkList

  if (isTask.value || isTool.value || isVideo.value) {
    let bgcolor = colors[linkList.value.length % colors.length]
    const _one = {...one.value, bgcolor, start: 0, end: 0, scheduled: false, group: 'default'}
    if (route.query.start && route.query.end) {
      _one.start = route.query.start
      _one.end = route.query.end
      _one.scheduled = true
      if (_one.sessionType == 'live') {
        calendarSessions.value.push(_one)
      }
    }
    linkList.value.unshift(_one)
    one.value.linkGroup.splice(1, 0, {_id: 'default', name: 'Current'})
  }

  await getSubLinksOfAll()

  liveList.value = linkList.value.filter((link) => link.sessionType == 'live')
  liveList.value.forEach((item) => {
    if (!liveGroup.value.includes(item.group)) {
      liveGroup.value.push(item.group)
    }
  })

  liveList.value.forEach((item) => {
    item.groupCount = liveGroup.value.length
  })
}

const getSubLinksOfAll = async () => {
  const linkIdsExcludeTool = []
  linkList.value.forEach((item) => {
    if (['task', 'pdTask'].includes(item.mode)) {
      linkIdsExcludeTool.push(item.id)
    }
  })
  let rs = {}
  if (isUnit.value) {
    rs = await allRelateLinkList({rid: linkIdsExcludeTool})
  }
  const newLinkList = []
  linkList.value.forEach((item) => {
    newLinkList.push({...item, ...{main: true}})
    if (rs[item.id]?.length) {
      rs[item.id].forEach((c) => {
        const link = item.link?.find((o) => o.id == c._id)
        const pidGroupName = item.linkGroup.find((group) => group._id == link.group)?.name
        newLinkList.push({...c, ...{group: item.group, pidGroup: link?.group, pidGroupName, parent: item._id}})
      })
    }
  })
  linkList.value = newLinkList
}

const getSubLinksOf = (link, type) => {
  return linkListSorted.value.filter((item) => item.parent == link._id && item.pidGroupName.indexOf(type) !== -1)
}

const getSubLinks = (link) => {
  return linkListSorted.value.filter((item) => item.parent == link._id)
}

const getLiveLinksByGroup = (group) => {
  return liveList.value.filter((link) => link.group == group._id)
}

const getAllLinksByGroup = (group) => {
  return linkList.value.filter((link) => link.group == group._id)
}

const getMainLinksByGroup = (group) => {
  return linkListSorted.value.filter((link) => link.group == group._id && link.main)
}

const linkClasses = (link) => {
  return {
    [`cursor-pointer text-white bg-${link.bgcolor}`]: link.scheduled || link.imported ? true : false,
    'cursor-move text-black bg-white border-negative': !link.scheduled && !link.imported ? true : false,
  }
}

const onLinkClicked = (link) => {
  if (link.scheduled || link.imported) {
    currentSession.value = {...link, ...{clickTime: new Date()}}
  } else {
    if ($q.screen.lt.sm) {
      if (touchScope.value) {
        addSessionToCalendar(link, touchScope.value)
        leftDrawerOpen.value = false
        touchScope.value = null
      }
    }
  }
}

const checkedLinkContents = ref([])
/*
const onLinkContentActive = (link) => {
  onLinkStartClicked(link)
  onLinkEndClicked(link)
  initFlatStartAndEnd()
  getCheckedLinkContens()
  checkGroupAll()
}
*/
const onLinkContentSync = async (targets, link) => {
  const {start, deadline, countdownType, countdownNum, block, category, color, tagId} = linkListSorted.value.find((e) => e._id == link._id)
  linkListSorted.value.forEach((e) => {
    if (targets?.includes(e._id)) {
      e.start = start
      e.expanded = true
      deadline ? (e.deadline = deadline) : delete e.deadline
      countdownType ? (e.countdownType = countdownType) : delete e.countdownType
      countdownNum ? (e.countdownNum = countdownNum) : delete e.countdownNum
      block ? (e.block = block) : delete e.block
      category ? (e.category = category) : delete e.category
      color ? (e.color = color) : delete e.color
      tagId ? (e.tagId = tagId) : delete e.tagId
    }
  })
  await sleep(300)
  linkListSorted.value.forEach((e) => {
    if (targets?.includes(e._id)) {
      delete e.expanded
    }
  })
}

const onLinkContentChange = (data, link) => {
  const _link = linkListSorted.value.find((e) => e._id == link._id)
  Object.keys(data).forEach((e) => {
    _link[e] = data[e]
  })
}

const showLiveSessionTip = ref(false)
const getCheckedLinkContens = () => {
  checkedLinkContents.value = []
  linkListSorted.value.forEach((item) => {
    if (item.startActive && item.endActive) {
      checkedLinkContents.value.push(item)
    }
  })

  if (checkedLinkContents.value.length == linkListSorted.value.length) {
    checkAllSessions.value = true
  } else {
    checkAllSessions.value = false
  }

  if (checkedLinkContents.value.length == 1 && checkedLinkContents.value[0]['sessionType'] == 'live') {
    showLiveSessionTip.value = true
  } else {
    showLiveSessionTip.value = false
  }

  if (checkedLinkContents.value.length == 1) {
    const linkContent = checkedLinkContents.value[0]
    if (linkContent.previous) {
      startOption.value = true
      startDate.value = null
      _startDate.value = linkContent.start
    } else {
      startDate.value = linkContent.start
    }
    if (linkContent.countdownType == 1) {
      endOption.value = 'deadline'
      endDate.value = linkContent.deadline
    } else if (linkContent.countdownType == 2) {
      endOption.value = 'countdown'
      selectedCountdown.value = linkContent.countdownNum
    }
    disableEndOpion.value = false
    setStartDateFirst.value = false
  } else if (checkedLinkContents.value.length > 1) {
    const sorted = checkedLinkContents.value.toSorted((a, b) => {
      return new Date(b?.start) - new Date(a?.start)
    })
    startDate.value = null
    _startDate.value = sorted[0]['start']
    startOption.value = false
    endDate.value = null
    endOption.value = null
    selectedCountdown.value = null
    //disableEndOpion.value = true
    //await sleep(300)
    //flatStartRef.value.click()
  }
}

const onAllClicked = (val) => {
  linkListSorted.value.forEach((item) => {
    item.startActive = val
    item.endActive = val
  })

  initFlatStartAndEnd()
  getCheckedLinkContens()
  checkGroupAll()
}

// const onLinkStartClicked = (link) => {
//   linkListSorted.value.forEach((item) => {
//     if (item._id === link._id && item.group === link.group) {
//       if ((link.parent && link.parent == item.parent) || !link.parent) {
//         item.startActive = !item.startActive
//       }
//     } else {
//       //item.startActive = false
//       //item.endActive = false
//     }
//   })
//   //checkGroupAll()
// }

// const onLinkEndClicked = (link) => {
//   linkListSorted.value.forEach((item) => {
//     if (item._id === link._id && item.group === link.group) {
//       if ((link.parent && link.parent == item.parent) || !link.parent) {
//         item.endActive = !item.endActive
//       }
//     } else {
//       //item.startActive = false
//       //item.endActive = false
//     }
//   })
//   //checkGroupAll()
// }

// const onGroupAllClick = (group) => {
//   group.allActive = !group.allActive
//   linkListSorted.value.forEach((link) => {
//     if (link.group == group._id) {
//       link.startActive = group.allActive
//       link.endActive = group.allActive
//     }
//   })

//   checkGroupAll()
//   initFlatStartAndEnd()
// }

const onStartOptionClick = () => {
  initFlatStartAndEnd()
  if (startOption.value) {
    console.log('The previous session ends checked!')
    setStart(null)
    startDate.value = null
  }
  if (startDate.value || startOption.value) {
    disableEndOpion.value = false
    setStartDateFirst.value = false
  } else {
    //disableEndOpion.value = true
  }
}

const onCountdownUpdate = async () => {
  await sleep(200)
  setEnd(null)
}

const setStartDateFirst = ref(false)
const onEndSectionClicked = () => {
  if (checkedLinkContents.value.length > 1 && (!startDate.value || !startOption.value)) {
    //setStartDateFirst.value = true
  }
}

const onEndOptionUpdate = async (value) => {
  if (value == 'deadline') {
    initFlatStartAndEnd()
    await sleep(200)
    flatEndRef.value.click()
  } else {
    await sleep(100)
    countdownRef.value.showPopup()
  }
}

const onStartDateUpdate = (e) => {
  startOption.value = false
  endDate.value = null
  endOption.value = null
  setStart(e.target.value)

  if (e.target.value || startOption.value) {
    disableEndOpion.value = false
    setStartDateFirst.value = false
  } else {
    //disableEndOpion.value = true
  }
}

const onEndDateUpdate = (e) => {
  setEnd(e.target.value)
}

const getPreviousLiveLink = (startIndex, source) => {
  let previousLiveLink = null

  let _linkLists = linkListSorted.value
  if (source) {
    _linkLists = source
  }
  while (startIndex > 0 && !previousLiveLink) {
    startIndex--
    const link = _linkLists[startIndex]
    if (link && link.sessionType == 'live') {
      previousLiveLink = link
    }
  }
  return previousLiveLink
}

const setStart = (start) => {
  linkListSorted.value.forEach((link, index) => {
    if (link.sessionType == 'live') return
    if (link.startActive) {
      if (startOption.value) {
        let previousLiveLink = getPreviousLiveLink(index)
        if (previousLiveLink) {
          link.start = previousLiveLink.end
          link.previous = true
          link.hasPrevious = true
        } else {
          link.start = date.formatDate(new Date(), 'YYYY-MM-DD HH:mm')
          link.previous = false
        }
      } else if (start) {
        link.start = start
        link.previous = false
      }

      if (start || startOption.value) {
        endOption.value = null
        endDate.value = null
        link.countdownType = 0
        link.countdownNum = null
        link.deadline = null
      }
    }
  })

  setStartToPreviousLiveSession()
}

const setEnd = (end) => {
  linkListSorted.value.forEach((link) => {
    if (link.sessionType == 'live') return
    if (link.endActive) {
      if (endOption.value === 'deadline' && end) {
        link.countdownType = 1
        link.countdownNum = 0
        link.deadline = end
      } else if (endOption.value === 'countdown' && selectedCountdown.value) {
        link.countdownType = 2
        link.countdownNum = selectedCountdown.value
        link.deadline = null
      }
    }
  })
}

const initFlatStartAndEnd = async () => {
  await sleep(200)
  initFlatStart()
  initFlatEnd()
}

const hasStartActive = ref(false)
const hasEndActive = ref(false)
const checkGroupAll = () => {
  const groupNotAllActive = {}
  hasStartActive.value = false
  hasEndActive.value = false

  linkListSorted.value.forEach((link) => {
    if (link.sessionType != 'live' && link.startActive) {
      hasStartActive.value = true
    }
    if (link.sessionType != 'live' && link.endActive) {
      hasEndActive.value = true
    }

    if (link.sessionType != 'live' && (!link.startActive || !link.endActive)) {
      groupNotAllActive[link.group] = true
    }
  })

  let linkGroup = one.value?.linkGroup
  if (isReschedule.value) {
    linkGroup = targetSession.value.task.linkGroup
  }
  linkGroup.forEach((group) => {
    if (groupNotAllActive[group._id]) {
      group.allActive = false
    } else {
      group.allActive = true
    }
  })

  if (!hasStartActive.value) {
    resetSettings('start')
  }

  if (!hasEndActive.value) {
    resetSettings('end')
  }

  //initFlatStartAndEnd()
  //setStartEnd(startDate.value, endDate.value)
}

const resetSettings = (type) => {
  if (type == 'start') {
    startOption.value = false
    startDate.value = null
  } else {
    endDate.value = null
    endOption.value = null
    selectedCountdown.value = null
  }
}

const onDragStart = (event, link) => {
  event.dataTransfer.dropEffect = 'copy'
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('_id', link._id)
  event.dataTransfer.setData('bgcolor', link.bgcolor)
  event.dataTransfer.setData('name', link.name)
}

const getServiceAuth = async () => {
  serviceTypes.value = await subjects.getOptions('1', 'pd')
  const auth = await App.service('service-auth').find({query: {status: 2, $limit: 1000}})
  const isService = ['pdUnit', 'pdTask'].includes(one.value.mode)
  let rs = null
  if (isService) {
    rs = auth.data?.find((item) => item.curriculum == 'pd' && item.subject == one.value?.service?.type?.[0])
  } else {
    rs = auth.data?.find((item) => item.curriculum == one.value.curriculum)
  }

  if (rs) {
    isPremium.value = true
  }
}

const getClassesOptions = async () => {
  if (pub.user.schoolInfo?._id) {
    classesList.value = await App.service('school-user').get('classList', {query: {school: pub.user.schoolInfo._id}})
  } else {
    const rs = await App.service('classes').find({query: {del: false}})
    classesList.value = rs.data
  }

  classesOptions.value = [
    {
      value: 'standard',
      label: 'Standard',
      disable: true,
      isSubtitle: true,
    },
  ]
  classesList.value
    .filter((e) => e?.type === 'standard')
    .forEach((item) => {
      classesOptions.value.push({label: item.name, classId: item._id, schoolId: item.school, type: item.type, enroll: item?.enroll || null})
    })
  classesOptions.value.push({
    value: 'subject',
    label: 'Subject',
    disable: true,
    isSubtitle: true,
  })
  classesList.value
    .filter((e) => e?.type === 'subject')
    .forEach((item) => {
      classesOptions.value.push({label: item.name, classId: item._id, schoolId: item.school, type: item.type, enroll: item?.enroll || null})
    })

  let _selectedClass = null
  if (route.hash) {
    classesOptions.value.forEach((item) => {
      if (route.hash.includes(item.classId)) {
        _selectedClass = item
        classIsAssigned.value = true
        disableClass.value = true
      }
    })
  } else if (classesOptions.value.length == 1) {
    _selectedClass = classesOptions.value[0]
  }

  if (_selectedClass) {
    form.value.selectedClass = _selectedClass
  }

  if (form.value.selectedClass?.classId) {
    onClassChanged(form.value.selectedClass)
  }
}
/*
  calendar
*/
const calendarSessions = ref([])

const onCalendarHide = () => {
  showDeadline.value = false
}

const onCalendarSave = async (obj) => {
  const {reg, start, end, id, recurFrequency, recurType} = {...obj}
  if (reg) {
    form.value.regDate = reg
    showDeadline.value = false
    stepper.value.next()
  }
  let session = null
  if (id) {
    linkList.value.forEach((item) => {
      if (item._id == id) {
        item.start = start
        item.end = end
        item.scheduled = true
        session = item
      }
    })
    const calendarSession = calendarSessions.value.find((item) => item._id == id)
    if (calendarSession) {
      if (calendarSession.arranged && calendarSession.uid == pub.user._id) {
        await App.service('session').patch(calendarSession._id, {start: new Date(start).toISOString(), end: new Date(end).toISOString()})
      }
    } else if (session) {
      calendarSessions.value.push(session)
    }
  }

  let needRecur = false
  let linkSeq = 0
  let groupSeq = 0
  let skipMonth = 0
  let links = null
  if (id && recurFrequency && recurType) {
    //one.value.linkGroup.forEach((group, index) => {
    liveGroup.value.forEach((group) => {
      if (needRecur) {
        links = getLiveLinksByGroup({_id: group})
        groupSeq += 1
        links.forEach((link, lidx) => {
          if (lidx == linkSeq) {
            let _start = date.addToDate(new Date(start), {days: 7 * groupSeq * recurFrequency})
            if (recurType == 'month') {
              _start = date.addToDate(new Date(start), {months: skipMonth + groupSeq * recurFrequency})
              while (new Date(_start).getDate() !== new Date(start).getDate()) {
                skipMonth += 1
                _start = date.addToDate(new Date(start), {months: skipMonth + groupSeq * recurFrequency})
              }
            }

            let _end = date.addToDate(new Date(_start), {minutes: session.duration})
            link.start = date.formatDate(new Date(_start), 'YYYY-MM-DD HH:mm')
            link.end = date.formatDate(new Date(_end), 'YYYY-MM-DD HH:mm')
            link.scheduled = true
            link.recurFrequency = session.recurFrequency
            link.recurType = session.recurType
            let _session = calendarSessions.value.find((s) => s._id === link._id)
            if (!_session) {
              calendarSessions.value.push(link)
            } else {
              _session = link
            }
          }
        })
      }
      if (group == session.group) {
        needRecur = true
        links = getLiveLinksByGroup({_id: group})
        links.forEach((link, lidx) => {
          if (link._id == id) {
            linkSeq = lidx
          }
        })
      }
    })
  }
}
const addSessionToCalendar = (session, scope) => {
  session.date = scope.timestamp.date
  session.start = date.formatDate(new Date(scope.timestamp.date + ' ' + scope.timestamp.time), 'YYYY-MM-DD HH:mm')
  session.end = date.formatDate(new Date(date.addToDate(new Date(session.start), {minutes: 30})), 'YYYY-MM-DD HH:mm')
  session.scheduled = true
  calendarSessions.value.push(session)
}

const onCalendarTouch = (scope) => {
  if (new Date(`${scope.timestamp.date} ${scope.timestamp.time}`) < new Date()) {
    return false
  }

  if (isImportToSession.value) {
    const session = liveList.value.find((item) => !item.readonly && !item.scheduled)

    if (session) {
      addSessionToCalendar(session, scope)
    }
  } else {
    leftDrawerOpen.value = true
    touchScope.value = scope
  }
}

const onSessionRemove = (id) => {
  linkList.value.forEach((item) => {
    if (item._id == id) {
      item.scheduled = false
      item.days = 0
    }
  })
}

const getWeekStartAndEnd = (val) => {
  let _date = new Date()
  if (val) {
    _date = new Date(val)
  }
  const dayOfWeek = _date.getDay()
  const _startDate = new Date(_date)
  const _endDate = new Date(_date)

  _startDate.setDate(_date.getDate() - dayOfWeek)
  _endDate.setDate(_date.getDate() - dayOfWeek + 6)

  return {start: date.formatDate(new Date(_startDate), 'YYYY-MM-DD'), end: date.formatDate(new Date(_endDate), 'YYYY-MM-DD')}
}

const getScheduledSessions = async () => {
  let start = null
  let end = null
  if (calendarStartDate.value && calendarEndDate.value) {
    start = new Date(date.startOfDate(calendarStartDate.value, 'day')).toISOString()
    const endDate = date.addToDate(new Date(calendarEndDate.value), {day: 1})
    end = new Date(date.startOfDate(endDate, 'day')).toISOString()
  } else {
    const startAndEndOfThisWeek = getWeekStartAndEnd()
    start = new Date(date.startOfDate(startAndEndOfThisWeek.start, 'day')).toISOString()
    const endDate = date.addToDate(new Date(startAndEndOfThisWeek.end), {day: 1})
    end = new Date(date.startOfDate(endDate, 'day')).toISOString()
  }

  const query = {start, end, zone: 0}
  calendarListQuery = {start, end, zone: 0}

  if (isEducator.value) {
    if (isPersonal.value) {
      query.type = 'workshop'
    } else {
      if (isPublic.value) {
        query.type = 'pdSchoolTeacherWorkshop'
      } else {
        query.type = 'pdSchoolWorkshop'
      }
    }
  } else {
    if (isPublic.value) {
      if (isPersonal.value) {
        query.type = {$in: ['taskWorkshop', 'studentWorkshop']}
      } else {
        query.type = {$in: ['taskSchoolWorkshop', 'pdSchoolStudentWorkshop']}
      }
    } else {
      query.type = {$in: ['pdClassSession', 'session']}
      if (form.value.selectedClass?.classId) {
        query.classId = form.value.selectedClass.classId
      } else {
        return
      }
    }
  }

  if (!isPersonal.value) {
    query.school = pub.user.schoolInfo?._id
  }

  calendarListSessions = await App.service('session').get('calendarList', {query})
  updateCalendarAllSessions(calendarListSessions)
  if (showAllMyLiveSessions) {
    onCalendarToggle(true)
  }
}

const updateCalendarAllSessions = (sessions) => {
  allSessions.value = sessions ?? []
  calendarSessions.value = calendarSessions.value.filter((item) => !item.arranged)
  allSessions.value.forEach((item) => {
    const exist = linkList.value.find((link) => link._id == item._id)
    if (exist && isReschedule.value) {
      item.bgcolor = exist.bgcolor
      item.reschedule = true
    }
    item.arranged = true
    calendarSessions.value.push(item)
  })
}

const mergeArrays = (array1, array2) => {
  const mergedArray = array1.concat(array2)
  const uniqueArray = mergedArray.filter((item, index, self) => index === self.findIndex((t) => t._id === item._id))
  return uniqueArray
}

const onCalendarToggle = async (val) => {
  showAllMyLiveSessions = val
  if (!calendarListMyLiveSessions) {
    $q.loading.show()
    const query = calendarListQuery
    // 所有我上的课
    const facilitate = await App.service('session').get('calendarList', {query})
    // 所有我参与的课
    query.$or = [{'reg._id': pub.user._id}, {students: pub.user._id}]
    const participate = await App.service('session').get('calendarList', {query})
    calendarListMyLiveSessions = [...facilitate, ...participate]
    $q.loading.hide()
  }

  if (val) {
    updateCalendarAllSessions(mergeArrays(calendarListSessions, calendarListMyLiveSessions))
  } else {
    updateCalendarAllSessions(calendarListSessions)
  }
}

const onCalendarChange = async (start, end) => {
  calendarStartDate.value = start
  calendarEndDate.value = end
  calendarListSessions = null
  calendarListMyLiveSessions = null
  if (!loading.value) {
    getScheduledSessions()
  }
}
</script>

<style lang="sass">
.schedule-stepper
  .q-stepper__tab
    width: 20% !important
.schedule-course-container
  .q-stepper
    box-shadow: none
  .q-stepper__header
    display: none
  .q-stepper__step-inner
    padding-bottom: 0
    padding-top: 8px
    padding-left: 8px
    padding-right: 8px
  .q-scrollarea__content
    width:100%
</style>
<style lang="sass" scoped>
.border-grey
   border: 1px solid $grey-4
.border-primary
   border: 1px solid $primary
.border-negative
   border: 1px solid $negative !important
.cursor-move
  cursor: move
.my-categories
  min-width: 550px
.date-input
  border: 1px solid #ccc
  border-radius: 3px
  padding: 0.54rem
  background: transparent
  &:focus
    border-color: $primary
</style>
