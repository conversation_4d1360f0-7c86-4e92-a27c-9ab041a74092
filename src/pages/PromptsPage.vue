<template>
  <q-layout view="hHh LpR fFf">
    <PubHeader />
    <q-page-container class="pc-sm">
      <q-page class="q-pa-md">
        <div class="text-h5 text-weight-medium q-mb-md">
          <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
          {{ isEditing ? 'Edit prompt' : 'View slides' }}
        </div>
        <div v-if="loading" class="text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
        <template v-else-if="one?.pages?.length">
          <div class="q-pa-md">
            <div class="row q-col-gutter-md">
              <div class="col-xs-12 col-sm-7">
                <div class="shadow-1 rounded-borders-md overflow-hidden" v-if="one?.pages">
                  <SlideList v-model="show" :no-thumbnail="!isEditing" :list="one.pages" :prompts="isEditing" @update="toSlideUpdate" @check="onSlideCheck" />
                </div>
                <div class="row justify-end q-pa-md" v-if="!isEditing && !route.path.includes('order')">
                  <q-btn color="primary" label="Unpublish" rounded no-caps @click="onUnpublishClick"></q-btn>
                </div>
              </div>
              <div class="col-xs-12 col-sm-5">
                <div class="shadow-1 rounded-borders-md overflow-hidden">
                  <TaskEditAddon :slideId="one.sid" :page="page" v-if="isEditing && !one.isEdit && one.sid" />
                  <TaskEditAddonCom :readOnly="route.path.includes('order')" :pageData="one" v-if="!isEditing && one._id" />
                </div>
              </div>
            </div>
          </div>
        </template>
      </q-page>
    </q-page-container>
    <q-footer reveal elevated class="bg-white" v-if="isEditing">
      <q-toolbar>
        <q-space></q-space>
        <q-banner dense rounded class="bg-black text-white" v-if="someSlideHasNoTopic()">
          Please make sure all selected prompts have tags set under topics
        </q-banner>
        <q-btn class="q-mx-sm" :class="{fit: $q.screen.lt.sm}" color="primary" label="Back" rounded outline no-caps @click="goBack"></q-btn>
        <q-btn
          class="q-mx-sm"
          :class="{fit: $q.screen.lt.sm}"
          color="primary"
          label="Confirm"
          icon="svguse:/v2/icons.svg#check"
          :disabled="!selection?.length || someSlideHasNoTopic()"
          :loading="confirming"
          rounded
          no-caps
          @click="onConfirmClick"></q-btn>
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {ref, watch, onMounted, computed, inject} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import useUnit from 'src/composables/account/unit/useUnit.js'
import {pubStore} from 'stores/pub'
import {addonStore} from 'stores/addon'
import TaskEditAddonCom from 'pages/com/TaskEditAddonCom.vue'
import TaskEditAddon from 'pages/com/TaskEditAddon.vue'
import SlideList from 'src/components/SlideList.vue'

/*
  consts
*/

const route = useRoute()
const router = useRouter()
const addon = addonStore()
const pub = pubStore()
const loading = ref(false)
const stateMap = inject('StateMap')
const one = ref({})
const page = ref(0)
const field = ref({label: 'PPT', key: 'pages', type: 'ppt'})
const selection = ref([])
const show = ref(0)
const isEditing = ref(route.params.action == 'edit')
const confirming = ref(false)
const {getOneById} = useUnit()

/*
  computed
*/

/*
  methods
*/

const onUnpublishClick = () => {
  $q.dialog({
    title: 'Confirm unpublish',
    message: `Are you sure you want to unpublish this prompt`,
    cancel: true,
  }).onOk(async () => {
    loading.value = true
    await App.service('prompts').patch(route.params.id, {publish: false})
    goBack()
    $q.notify({type: 'info', message: 'Unpublish successfully'})
  })
}

const someSlideHasNoTopic = () => {
  return selection.value.some((e) => !addon.questions?.find((item) => item.page == e._id)?.outlines?.outline?.length)
}

const onSlideCheck = (val) => {
  selection.value = val
}

const onConfirmClick = async () => {
  show.value += 1
  if (someSlideHasNoTopic()) {
    $q.notify({message: 'Please make sure all selected prompts have tags set under topics'})
  } else {
    //TODO
    const {curriculum, service, subjects, grades} = {...one.value}

    confirming.value = true
    for (let i = 0; i < selection.value.length; i++) {
      const page = selection.value[i]
      const pages = []
      const questions = []
      const materials = []
      pages.push(page)
      questions.push(addon.questions?.find((e) => e.page == page._id) || {})
      materials.push(addon.materials?.find((e) => e.page == page._id) || {})

      await App.service('prompts').create({unit: one.value._id, publish: true, pages, questions, materials, curriculum, service, subjects, grades})
    }

    confirming.value = false
    router.replace('/home/<USER>')
    $q.notify({
      message: 'Your selected slides have been published as prompts, you can manage your prompts by clicking “Published” page from the main navigation bar.',
    })
  }
}

const toSlideUpdate = (i) => {
  page.value = i
  if (isEditing.value) {
    router.replace({query: {...route.query, ...{page: page.value}}})
  }
}

const goBack = async () => {
  await router.replace(route.query.back || '/home/<USER>')
  if (!isEditing.value) {
    location.reload()
  }
}

const main = async () => {
  const {state, id} = route.params

  if (!id) return
  loading.value = true
  if (isEditing.value) {
    one.value = await getOneById(id)
  } else {
    one.value = await App.service('prompts').get(id)
  }
  loading.value = false
  if (pub.user._id) {
  }
}

onMounted(main)
</script>
<style lang="sass" scope>
.border-1
  border: 1px solid $grey-3
.video-item
  width:100%
</style>
