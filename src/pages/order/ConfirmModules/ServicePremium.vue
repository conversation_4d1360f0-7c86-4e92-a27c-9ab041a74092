<template>
  <div class="q-px-md q-mb-md" v-if="servicePackData">
    <q-banner inline-actions rounded class="bg-amber-2 text-black q-mb-md" v-if="service_premium_tips">
      The Premium lecture courses require your school to make a unified purchase in order to schedule sessions. If an individual student purchases a Premium
      lecture, the cost will be refunded to your school. Additionally, if an individual student purchases a mentor or carer service package, Classcipe will
      reward your school with a certain percentage of commission.
      <template v-slot:action>
        <q-btn flat icon="close" class="q-pa-sm" @click="service_premium_tips = false" />
      </template>
    </q-banner>
    <div class="row q-col-gutter-md">
      <div class="col-sm-8 col-xs-12">
        <q-card v-if="(servicePremiumRole === 'school' || servicePremiumRole === 'personal') && servicePackData?.splitSale" class="q-mb-md">
          <q-card-section>
            <div class="row items-center">
              <div class="col text-bold">Selected lecture contents ({{ list?.filter((v) => v?.type === 'lecture').length }})</div>
              <q-btn rounded flat no-caps dense icon="edit" class="text-grey-8" @click="selectLectureFn" />
            </div>
          </q-card-section>
        </q-card>
        <template v-if="servicePackData?.splitSale && (servicePremiumRole === 'school' || servicePremiumRole === 'personal')">
          <div class="text-weight-bold q-mb-md">Please choose at least one lecture content to purchase</div>
        </template>
        <q-item style="padding: 0" v-for="item in list" :key="item._id" class="row q-mt-md">
          <q-item-section class="col">
            <q-item-label v-if="item?.type === 'lecture'">
              <ServicePrePackage :item="item" :discount="totalDiscount" />
            </q-item-label>
            <q-item-label v-if="item?.type === 'carer'">
              <CarerPackage :item="{...item?.carerPackInfo, times: careTimes}" :discount="totalDiscount" />
            </q-item-label>
          </q-item-section>
        </q-item>
        <div class="rounded-borders-md q-mt-md q-pa-md" v-if="servicePremiumRole === 'school' && list.some((o) => o.servicePack)">
          <div class="row q-col-gutter-md">
            <div class="col-6 text-bold">
              <q-option-group
                :options="premiumContentOptions"
                color="primary"
                type="radio"
                v-model="premiumContentChoice"
                @update:model-value="serviceChooseChange" />
            </div>
            <div class="col-6" v-if="premiumContentChoice === 'both' || premiumContentChoice === 'mentoring'">
              <q-input type="number" v-model="personsBuy" outlined stack-label label="No of students" @update:model-value="serviceChooseChange" />
              <div class="text-bold q-mt-md">
                The number you enter determines the quantity of mentor service packages that your payment includes, make sure you check the number of students
                who already paid on the
                <q-btn
                  class="q-pa-xs text-underline"
                  flat
                  color="primary"
                  no-caps
                  label="application tracking list "
                  target="_blank"
                  href="/v2/premcpack/applicationTrack" />
                before you enter the number to avoid repetitive purchase.
              </div>
            </div>
          </div>
        </div>
        <q-card class="rounded-borders-md q-mt-md bg-white">
          <q-card-section>
            <template v-if="isPointMode">
              <div class="text-h6 q-mb-md">Points redemption policy</div>
              <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/points_redemption" />
            </template>
            <template v-else>
              <div class="text-h6 q-mb-md">Cancellation policy</div>
              <CancellationPolicy type="service" />
              <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/cancellation" />
            </template>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-sm-4 col-xs-12">
        <q-card class="rounded-borders-md">
          <q-card-section>
            <div class="text-h6">Order summary</div>
            <div class="row justify-between q-py-sm text-subtitle1">
              <div class="items-center flex">
                <span class=""> Subtotal({{ service_premium_choose.length }} items) </span>
              </div>
              <div>${{ (totalPrice / 100).toFixed(2) }}</div>
            </div>
            <div class="row justify-between q-py-sm text-subtitle1" v-for="item in service_premium_choose" :key="item._id">
              <div class="col ellipsis">
                <span class="text-capitalize">
                  <template v-if="item?.type === 'lecture'"> Lecture: </template>
                  <template v-if="item?.type === 'service'"> Mentoring service: </template>
                  <template v-if="item?.type === 'carer'"> Carer service: </template>
                  {{ item?.name }}
                  <q-tooltip>
                    {{ item?.name }}
                  </q-tooltip>
                </span>
              </div>
              <div>
                ${{ (item?.price / 100).toFixed(2) }}
                <template v-if="item?.type === 'service' || item?.type === 'carer'"> *{{ personsBuy }}</template>
              </div>
            </div>
            <div class="row justify-between q-py-sm text-subtitle1 text-weight-bold">
              <div class="items-center flex">
                <span class=""> Total </span>
              </div>
              <div>${{ (totalPrice / 100).toFixed(2) }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>
<script setup>
/*
  imports
*/
import {computed, inject, nextTick, onMounted, ref, watchEffect} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'

const pub = pubStore()

import CancellationPolicy from 'src/pages/order/CancellationPolicy.vue'
import ServicePrePackage from 'src/pages/order/components/ServicePrePackage.vue'
import CarerPackage from 'src/pages/order/components/CarerPackage.vue'
import {isTeacherRole, claimCategoryMap, calPremiumLecture, calPremiumMentor} from 'src/pages/order/consts.js'
import SelectLectureDialog from 'src/pages/order/components/SelectLectureDialog.vue'
import {calServiceDiscount} from 'src/pages/sys/package/const'
import AlertDialog from 'src/components/AlertDialog.vue'

const route = useRoute()
const router = useRouter()
const ids = ref(route.params.ids)
const dialogShow = ref(false)
const list = ref([])
const totalPrice = ref(0)
const totalPoint = ref(0)
const personsBuy = ref(1)

import {pointStore} from 'stores/point'

const pStore = pointStore()

const detail = ref(null)

const isTeacher = ref(isTeacherRole(pub?.user?.roles))

const isDiscount = ref(null)

const isPointMode = ref(!!route?.query?.isPointMode)
const servicePremiumRole = ref('')

const sharedSchool = ref(route?.query?.sharedSchool)
const schoolId = ref(sharedSchool.value ? undefined : pub?.user?.school || undefined)

const servicePackData = ref({})
const premiumContentChoice = ref('both')
const premiumContentOptions = [
  {
    label: 'Premium content lecture only',
    value: 'lecture',
  },
  {
    label: '1v1 mentor service only',
    value: 'mentoring',
  },
  {
    label: 'Both',
    value: 'both',
  },
]
const service_premium_choose = ref([])

const applyData = ref(null)

console.log('pub', pub.user)

const emit = defineEmits(['updateData'])

const service_premium_tips = ref(false)

const totalDiscount = computed(() => {
  return calServiceDiscount(servicePackData.value?.discountConfig)
})

const careTimes = computed(() => {
  return list.value
    ?.filter((f) => f.type === 'lecture')
    ?.reduce((sum, current) => {
      console.log('current', current)
      console.log('servicePackData', servicePackData.value)
      return sum + Math.ceil(current.times / Math.ceil(28 / servicePackData.value?.freq))
    }, 0)
})

const checkoutData = async () => {
  const isAdmin = pub?.user?.schoolUser?.role?.includes('admin')
  let school = (isAdmin && pub?.user?.school) || undefined
  let isSchool = isAdmin && !!pub?.user?.school
  let links = []
  let servicePremium = ids.value

  service_premium_choose.value?.forEach((item) => {
    links.push({
      id: item?.id,
      style: item?.style,
      count: item?.count,
    })
  })

  if (sharedSchool.value) {
    school = undefined
    isSchool = false
  }

  let isUpdate = false
  if (sharedSchool.value) {
    const schoolPrice = await App.service('service-pack-apply').find({
      query: {
        sharedSchool: sharedSchool.value,
        servicePack: ids.value,
        status: 1,
        uid: pub?.user?._id,
      },
    })

    let data = schoolPrice?.data?.[0] || {}

    if (data?.updatedAt !== applyData.value?.updatedAt) {
      isUpdate = true
    }
  } else {
    let data = (await App.service('service-pack').get(ids.value)) || {}
    if (data?.updatedAt !== servicePackData.value?.updatedAt) {
      isUpdate = true
    }
  }

  if (isUpdate) {
    $q.dialog({
      component: AlertDialog,
      componentProps: {
        title: 'The product has been updated, please confirm',
      },
    }).onOk(async (res) => {
      $q.loading.hide()
      initData()
    })
    return
  }

  return {
    linksQuery: {
      links,
      servicePremium,
      sharedSchool: sharedSchool.value,
    },
    orderQuery: {
      link: links,
      sharedSchool: sharedSchool.value,
      isPoint: isPointMode.value,
      inviter: route?.query?.inviteCode,
      servicePackApply: route?.query?.servicePackApply,
      persons: personsBuy.value,
      servicePremium,
      school,
      isSchool,
      inviteSource: route?.query?.inviteSource,
      inviteSourceId: route?.query?.inviteSourceId,
      schoolInviter: route?.query?.schoolInviter,
    },
  }
}

const selectLectureFn = () => {
  $q.dialog({
    component: SelectLectureDialog,
    componentProps: {
      servicePackId: ids.value,
      defaultChoose: list.value?.filter((item) => item?.type === 'lecture')?.map((item) => item?.premium?._id),
    },
  }).onOk(async (res) => {
    console.log()

    const newList = [...list.value].filter((v) => v?.type !== 'lecture')
    list.value = [...res, ...newList]
    serviceChooseChange()
  })
}

const serviceChooseChange = () => {
  let chooseList = []
  console.log('careTimes', careTimes.value)
  console.log(
    'list2',
    list.value?.filter((f) => f.choose)
  )

  if (premiumContentChoice.value === 'lecture') {
    personsBuy.value = 1
  }
  list.value
    ?.filter((f) => f.choose)
    ?.map((e) => {
      if (premiumContentChoice.value === 'lecture') {
        if (e.type === 'lecture') {
          const lecturePrice = calPremiumLecture({
            discountConfig: e?.discountConfig,
            price: schoolId.value ? e?.schoolPrice : e?.price,
            times: e?.times,
          })
          chooseList.push({
            id: e?.premium?._id,
            name: e?.premium?.unit?.name,
            times: e?.times,
            persons: 1,
            type: 'lecture',
            price: lecturePrice?.price,
            style: 'service_premium',
            count: e?.times,
          })
        }
      } else if (premiumContentChoice.value === 'mentoring') {
        if (e?.servicePack && e.type === 'lecture') {
          const mentoringPrice = calPremiumMentor({
            servicePack: e?.servicePack,
            count: e?.times,
            discount: totalDiscount.value,
          })
          chooseList.push({
            id: e?.servicePack?._id,
            name: e?.servicePack?.name,
            times: e?.times,
            persons: personsBuy.value,
            type: 'service',
            price: mentoringPrice?.price,
            style: 'service',
            count: e?.times,
          })
        }

        if (e.type === 'carer') {
          const carerPrice = calPremiumMentor({
            servicePack: e?.carerPackInfo,
            count: careTimes.value,
            discount: totalDiscount.value,
          })
          chooseList.push({
            id: e?.carerPackInfo?._id,
            name: e?.carerPackInfo?.name,
            times: careTimes.value,
            persons: personsBuy.value,
            type: 'carer',
            price: carerPrice?.price,
            style: 'service',
            count: careTimes.value,
          })
        }
      } else if (premiumContentChoice.value === 'both') {
        if (e.type === 'lecture') {
          const lecturePrice = calPremiumLecture({
            discountConfig: e?.discountConfig,
            price: schoolId.value ? e?.schoolPrice : e?.price,
            times: e?.times,
          })
          chooseList.push({
            id: e?.premium?._id,
            name: e?.premium?.unit?.name,
            times: e?.times,
            persons: 1,
            type: 'lecture',
            price: lecturePrice?.price,
            style: 'service_premium',
            count: e?.times,
          })
        }
        if (e?.servicePack && e.type === 'lecture') {
          const mentoringPrice = calPremiumMentor({
            servicePack: e?.servicePack,
            count: e?.times,
            discount: totalDiscount.value,
          })
          chooseList.push({
            id: e?.servicePack?._id,
            name: e?.servicePack?.name,
            times: e?.times,
            persons: personsBuy.value,
            type: 'service',
            price: mentoringPrice?.price,
            style: 'service',
            count: e?.times,
          })
        }
        if (e.type === 'carer') {
          const carerPrice = calPremiumMentor({
            servicePack: e?.carerPackInfo,
            count: careTimes.value,
            discount: totalDiscount.value,
          })
          chooseList.push({
            id: e?.carerPackInfo?._id,
            name: e?.carerPackInfo?.name,
            times: careTimes.value,
            persons: personsBuy.value,
            type: 'carer',
            price: carerPrice?.price,
            style: 'service',
            count: careTimes.value,
          })
          console.log('carer')
        }
      }
    })

  service_premium_choose.value = chooseList
  console.log('chooseList', chooseList)
  const sum = chooseList.reduce((accumulator, currentObject) => accumulator + currentObject.price * currentObject.persons, 0)
  totalPrice.value = sum
}

const main = async () => {
  let data = await App.service('service-pack').get(ids.value)
  servicePackData.value = data
  if (!data?.status) {
    dialogShow.value = true
    return
  }

  if (schoolId.value) {
    servicePremiumRole.value = 'school'
  } else {
    if (sharedSchool.value) {
      servicePremiumRole.value = 'school_student'

      const schoolPriceData = await App.service('service-pack-apply').find({
        query: {
          sharedSchool: sharedSchool.value,
          servicePack: ids.value,
          status: 1,
          uid: pub?.user?._id,
        },
      })

      console.log('schoolPriceData11', schoolPriceData)

      if (schoolPriceData?.total > 0) {
        data = {
          contentOrientated: schoolPriceData?.data?.[0]?.contentOrientated || [],
          carerPack: data?.carerPack,
          updatedAt: schoolPriceData?.data?.[0]?.updatedAt,
        }
        applyData.value = schoolPriceData?.data?.[0] || {}
      } else {
        data = {}
      }
    } else {
      servicePremiumRole.value = 'personal'
    }
  }

  let premiumIds = []
  let packageIds = []
  if (data?.contentOrientated) {
    data.contentOrientated.map((e) => {
      if (e.premium) {
        premiumIds.push(e.premium)
      }
      if (e.servicePack) {
        packageIds.push(e.servicePack)
      }
    })
  }

  if (premiumIds?.length) {
    const serviceAuths = await App.service('service-auth').get('unit', {query: {_id: {$in: premiumIds}}})
    data.contentOrientated.map((e) => {
      e.premium = serviceAuths.data?.find((f) => f._id == e.premium)
    })
  }

  if (packageIds?.length) {
    const servicePacks = await App.service('service-pack').find({query: {_id: {$in: packageIds}}})
    data.contentOrientated.map((e) => {
      e.servicePack = servicePacks.data?.find((f) => f._id == e.servicePack)
    })
  }

  if (data?.carerPack?._id) {
    const carerPackInfo = await App.service('service-pack').get(data?.carerPack?._id)
    if (carerPackInfo) {
      data.carerPackInfo = {
        ...carerPackInfo,
        // times:careTimes.value,
      }
    }
  }

  data.contentOrientated.map((e) => {
    e.discountConfig = data?.discountConfig
    e.type = 'lecture'
    e.splitSale = data.splitSale
    e.choose = true
  })

  console.log('data?.carerPack', data?.carerPack)

  list.value = data?.carerPackInfo
    ? [
        ...data.contentOrientated,
        {
          type: 'carer',
          carerPackInfo: data?.carerPackInfo,
          choose: true,
          // times:careTimes.value,
          _id: `carer-${data?.carerPackInfo?._id}`,
        },
      ]
    : [...data.contentOrientated]

  console.log('list', list.value)

  serviceChooseChange()
  console.log('data', data)

  if (!sharedSchool.value && schoolId.value) {
    await App.service('service-pack-school-price')
      .find({
        query: {
          servicePack: ids.value,
          school: schoolId.value,
        },
      })
      .then((res) => {
        console.log('res', res)
        if (res?.total > 0 && res?.data?.[0]?.priceEnable) {
          service_premium_tips.value = true
        }
      })
  }
}

const initData = () => {
  main().catch((err) => {
    dialogShow.value = true
    console.log('err', err)
  })
}

onMounted(initData)

watchEffect(() => {
  const data = {
    totalPrice: totalPrice.value,
    totalPoint: totalPoint.value,
    dialogShow: dialogShow.value,
  }
  emit('updateData', data)
})

defineExpose({checkoutData, initData})
</script>
<style lang="scss" scoped>
.text-underline {
  text-decoration: underline;
}
</style>
