<template>
  <q-layout view="hHh LpR fFf" class="bg-grey-1">
    <q-header class="bg-white text-black" elevated>
      <q-toolbar class="">
        <q-btn dense flat icon="navigate_before" round @click="goBack"></q-btn>
        <div class="col text-center">Confirm and pay</div>
        <AccountMenu :ifNotice="true" />
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm">
      <q-page class="">
        <div v-if="loading" class="text-center q-pa-xl text-grey">
          <q-spinner-ball class="full-width" color="primary" size="2em" />
        </div>
        <div v-if="detail">
          <div class="q-px-md">
            <div class="q-mr-lg">
              <div class="text-h6 q-my-md">Order summary</div>
              <div class="row justify-between q-py-sm text-subtitle1 text-weight-bold">
                <div class="items-center flex">
                  <span class=""> Total </span>
                </div>
                <div class="">${{ (detail.price / 100).toFixed(2) }}</div>
              </div>
              <div class="row justify-between q-py-sm">
                <div class="items-center flex">
                  <span class=""> Subtotal </span>
                </div>
                <div class="">${{ (detail.price / 100).toFixed(2) }}</div>
              </div>
            </div>
          </div>
          <div v-if="detail.status === 100" class="q-pa-md">
            <div class="text-center q-mt-xl text-grey-6 q-mb-md">
              Order will be closed in
              <CountDown :deadTime="detail.expiration" />
            </div>
            <div id="paypal-button" class="text-center q-mt-md"></div>
          </div>
          <q-btn
            v-if="detail.status === 200"
            @click="toDetail"
            class="q-mt-lg q-mx-md"
            :class="{'full-width': mobile}"
            color="primary"
            label="Check order details"
            no-caps
            rounded></q-btn>
          <div class="text-center bg-grey-1 q-pa-sm fixed-bottom">
            <span class="text-grey-6">Click to read</span>
            <span class="text-blue-7 q-ml-sm">Cancellation policy</span>
          </div>
        </div>
      </q-page>
    </q-page-container>
    <div>
      <q-dialog persistent v-model="payLoading" ref="dialogRef" :maximized="$q.screen.lt.sm" class="bg-grey">
        <div class="text-center q-pa-xl q-mt-md">
          <q-spinner color="white" size="3em" />
        </div>
      </q-dialog>
    </div>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {onMounted, ref, onUnmounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import AccountMenu from 'components/AccountMenu.vue'
import CountDown from './CountDown.vue'
import AbandonPayDialog from './AbandonPayDialog.vue'
import AlertDialog from 'src/components/AlertDialog.vue'
import {loadScript} from '@paypal/paypal-js'
import {pubStore} from 'stores/pub'
const pub = pubStore()

console.log('pub', pub?.isStudent)

const route = useRoute()
const router = useRouter()
const id = ref(route.params.id)
const myDeviceData = ref('')

const loading = ref(false)
const payLoading = ref(false)

const mobile = ref($q.platform.is.mobile)
const detail = ref(null)

const goBack = () => {
  $q.dialog({
    component: AbandonPayDialog,
  })
}

const toDetail = () => {
  router.push({
    path: `/order/detail/${id.value}`,
    query: {from: 'result'},
  })
}

const premiumBackUrl = (status) => {
  const promptText = status
    ? 'The product has been updated, unable to complete payment. Please select the product again.'
    : 'The product are no longer available'
  $q.dialog({
    component: AlertDialog,
    componentProps: {
      title: promptText,
    },
  }).onOk(async (res) => {
    App.service('order')
      .get('cancelBeforePay', {query: {id: id.value, status: '404'}})
      .then(() => {
        const backUrl = status
          ? `/service/pack/${detail.value?.servicePremium}?tab=featured&back=/home/<USER>
          : `/${pub?.isStudent ? 'study' : 'home'}/booking?tab=featured`

        console.log('backUrl', backUrl)
        router.push(backUrl)
      })
  })
}

const devPaypal = 'Acg70iZz0flbVOgEA1SjmiqEhKw850HHMnOjOp1F96UOjqgnF372WxO9QcbNPIOkGm9CROrrGYFmQ6Yh'
const proPaypal = 'AVj0ejw9ioxhqTWelmz-1h6UMQZaNrNGObZFWc0iQzJ94SvBVE98Vf88tLfZapi7JXJMd_jWLAmLEJ0g'

const paypalClientId = isDev ? devPaypal : proPaypal

const main = async () => {
  //  const tokenRes = await App.service('braintree').get('token', {});

  App.service('order').removeAllListeners(['patched'])
  App.service('order').on('patched', (patchedData) => {
    console.log('patchedData', patchedData)
    if (patchedData._id === id.value && patchedData.settled === true && patchedData.status !== 100) {
      router.push({
        path: `/order/payResult/${id.value}`,
        query: {
          payBack: route?.query?.payBack,
        },
      })
    }
  })

  loading.value = true

  const res = await App.service('order').get(id.value)
  detail.value = res
  loading.value = false

  //tokenRes?.clientToken
  // Create a client.

  if (res?.status !== 100) {
    return
  }

  loadScript({'client-id': paypalClientId})
    .then((paypal) => {
      paypal
        .Buttons({
          style: {
            layout: 'vertical',
            disableMaxWidth: true,
          },
          onClick: (data, actions) => {
            if (detail.value?.type === 'service_premium') {
              return App.service('service-pack')
                .get(detail.value?.servicePremium)
                .then(async (res) => {
                  console.log('res', res)
                  if (res?.updatedAt !== detail.value?.servicePremiumSnapshot?.updatedAt) {
                    premiumBackUrl(res?.status)
                    return actions.reject()
                  }
                })
                .catch((err) => {
                  premiumBackUrl(false)
                  return actions.reject()
                })
            } else {
              return actions.resolve()
            }
          },
          createOrder(data, actions) {
            console.log('createOrder')
            return App.service('paypal')
              .get('payment', {
                query: {
                  id: id.value, // orderId
                },
              })
              .then((order) => {
                return order.id
              })
          },
          onApprove: (data, actions) => {
            console.log('onApprove')
            return actions.order.capture().then(async (details) => {
              // payed completed
              console.log('payed', details)
              payLoading.value = true
              if (details.status === 'COMPLETED') {
                await App.service('order').get('checkPaypalPayment', {query: {id: id.value, paypalOrderId: details.id}})
              }
            })
          },
          onError: (err) => {
            console.log('payErr', err)
          },
        })
        .render('#paypal-button')
    })
    .catch((err) => {
      console.error('failed to load the PayPal JS SDK script', err)
    })

  return

  // Create a client.
  braintree.client
    .create({
      authorization: tokenRes?.clientToken,
    })
    .then(function (clientInstance) {
      braintree.dataCollector.create(
        {
          client: clientInstance,
        },
        function (err, dataCollectorInstance) {
          if (err) {
            // Handle error
            return
          }
          // At this point, you should access the dataCollectorInstance.deviceData value and provide it
          // to your server, e.g. by injecting it into your form as a hidden input
          myDeviceData.value = dataCollectorInstance.deviceData
          console.log('myDeviceData', myDeviceData.value)
        }
      )

      // Create a PayPal Checkout component.
      return braintree.paypalCheckout.create({
        client: clientInstance,
      })
    })
    .then(function (paypalCheckoutInstance) {
      return paypalCheckoutInstance.loadPayPalSDK({
        currency: 'USD',
        intent: 'capture',
      })
    })
    .then(function (paypalCheckoutInstance) {
      return paypal
        .Buttons({
          fundingSource: paypal.FUNDING.PAYPAL,
          createOrder: function () {
            return paypalCheckoutInstance.createPayment({
              flow: 'checkout', // Required
              amount: +(detail?.value.price / 100).toFixed(2), // Required
              currency: 'USD', // Required, must match the currency passed in with loadPayPalSDK
              intent: 'capture', // Must match the intent passed in with loadPayPalSDK
              enableShippingAddress: true,
              shippingAddressEditable: false,
            })
          },

          onApprove: function (data, actions) {
            return paypalCheckoutInstance.tokenizePayment(data).then(async function (payload) {
              App.service('braintree').timeout = 100000
              await App.service('braintree')
                .get('sale', {
                  query: {
                    nonce: payload.nonce,
                    deviceData: myDeviceData.value,
                    orderId: id.value,
                  },
                })
                .then(() => {
                  $q.notify({type: 'positive', message: 'payment successfully'})
                  router.push(`/order/payResult/${id.value}`)
                })
                .catch((err) => {
                  $q.notify({type: 'negative', message: err.message})
                })
              // Submit 'payload.nonce' to your server
            })
          },

          onCancel: function (data) {
            console.log('PayPal payment cancelled', JSON.stringify(data, 0, 2))
          },

          onError: function (err) {
            console.error('PayPal error', err)
          },
        })
        .render('#paypal-button')
    })
    .then(function () {
      // The PayPal button will be rendered in an html element with the ID
      // 'paypal-button'. This function will be called when the PayPal button
      // is set up and ready to be used
    })
}
onMounted(main)

onUnmounted(() => {
  console.log('leaving pay confirm')
  App.service('order').removeAllListeners(['patched'])
})
</script>
<style lang="sass" scope></style>
