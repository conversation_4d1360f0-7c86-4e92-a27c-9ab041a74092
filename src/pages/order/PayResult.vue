<template>
  <q-layout view="hHh LpR fFf" class="bg-grey-1">
    <q-header class="bg-white text-black" elevated>
      <q-toolbar class="">
        <!-- <q-btn dense flat icon="navigate_before" round @click="goBack"></q-btn> -->
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm">
      <q-page class="">
        <div class="q-px-md">
          <div class="text-h2 text-bold text-primary q-pt-lg" :class="{'text-h4': mobile}">Thanks for your payment</div>

          <!-- Processing status message -->
          <div v-if="detail && detail.status === 110" class="q-mt-lg">
            <div class="q-pa-md bg-orange-1 rounded-borders">
              <div class="row items-center">
                <q-spinner-dots color="orange" size="2em" class="q-mr-md" />
                <div>
                  <div class="text-h6 text-orange-8">Your order is being processed</div>
                  <div class="text-body2 text-grey-7">Please wait while we confirm your payment and prepare your order. This usually takes a few moments.</div>
                </div>
              </div>
            </div>
            <!-- Only show "Check order details" button for processing status -->
            <q-btn @click="toDetail" class="q-mt-lg" :class="{'full-width': mobile}" color="primary" label="Check order details" no-caps rounded />
          </div>

          <!-- Success status content -->
          <div v-if="detail && detail.status === 200">
            <div class="q-mt-md text-bold" v-if="bookDetail?.session">The session has been successfully scheduled with your purchased content</div>
            <q-btn @click="toDetail" class="q-mt-lg q-mr-md" :class="{'full-width': mobile}" color="primary" label="Check order details" no-caps rounded />
            <q-btn
              v-if="isVoucher"
              outline
              class="q-mt-lg"
              :class="{'full-width': mobile}"
              color="primary"
              :label="`Claim the voucher(${detail?.persons})`"
              no-caps
              rounded
              @click="toApplication" />

            <q-btn
              v-if="bookDetail?.session"
              outline
              class="q-mt-lg"
              :class="{'full-width': mobile}"
              color="primary"
              label="Go to the scheduled session"
              no-caps
              rounded
              @click="toSession" />
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {computed, inject, onMounted, onUnmounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {useQuasar} from 'quasar'
import {pubStore} from 'stores/pub'
const $q = useQuasar()
const pub = pubStore()
const mobile = ref($q.platform.is.mobile)

const route = useRoute()
const router = useRouter()
const id = ref(route.params.id)
const detail = ref(null)
const bookDetail = ref({})

const toDetail = () => {
  router.push({
    path: `/order/detail/${id.value}`,
    query: {from: 'result'},
  })
}

const toApplication = () => {
  console.log('detail', detail.value)
  router.push({
    path: `/premcpack/applicationTrackDetail/${detail.value?.servicePremium}`,
    query: {tab: 1},
  })
}

const toSession = () => {
  router.push({
    path: `/detail/session/${bookDetail.value?.session?._id}`,
  })
}

const isVoucher = computed(() => {
  return detail.value?.type === 'service_premium' && pub?.user?.school
})

const getBookDetail = async (res = {}) => {
  // Only fetch book details when order status is 200 (paid)
  if (res.status !== 200) {
    return
  }

  const {links = []} = res
  let bookId = links.find((item) => item.bookingId)?.bookingId
  if (bookId) {
    console.log('bookId', bookId)
    $q.loading.show()
    await App.service('service-booking')
      .get(bookId)
      .then((data) => {
        console.log('data', data)
        bookDetail.value = data
      })
      .finally(() => {
        $q.loading.hide()
      })
  }
}

const updatePub = async () => {
  const schoolUser = pub?.user?.schoolUser
  await pub.init(true)
  if (schoolUser) {
    await pub.switchAccount(schoolUser)
  }
  console.log('pub', pub?.user)
}

const needBack = () => {
  if (route?.query?.payBack) {
    router.push(decodeURIComponent(route?.query?.payBack))
  }
}

const handleOrderUpdate = async (orderData) => {
  console.log('Order updated:', orderData)
  detail.value = orderData

  // If status changed to 200, fetch book details
  if (orderData.status === 200) {
    await getBookDetail(orderData)
  }
}

onMounted(async () => {
  // Set up socket listener for order updates
  App.service('order').removeAllListeners(['patched'])
  App.service('order').on('patched', (patchedData) => {
    console.log('Order patched:', patchedData)
    if (patchedData._id === id.value) {
      handleOrderUpdate(patchedData)
    }
  })

  updatePub()
  $q.loading.show()
  await App.service('order')
    .get(id.value)
    .then((res) => {
      if (res?.type === 'prompt') {
        if (top) {
          top.postMessage({app: 'web', action: 'prompt', order: res}, '*')
        }
      }
      detail.value = res
      needBack()
      // Only call getBookDetail if status is 200
      getBookDetail(res)
    })
    .finally(() => {
      $q.loading.hide()
    })
})

onUnmounted(() => {
  // Clean up socket listeners
  App.service('order').removeAllListeners(['patched'])
})
</script>
<style lang="sass" scope></style>
