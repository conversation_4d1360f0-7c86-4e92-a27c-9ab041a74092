export const ORDER_STATUS = {
  100: {
    tips: '',
    label: 'Unpaid',
  },
  110: {
    tips: 'Payment successful! Please wait while we process your order.',
    label: 'Processing',
  },
  200: {
    tips: 'Paid successfully',
    label: 'Paid',
  },
  400: {
    tips: 'Payment has timed out',
    label: 'Canceled',
  },
  404: {
    tips: 'Order has been cancelled',
    label: 'Canceled',
  },
  500: {
    tips: 'canceled by the purchaser',
    label: 'Canceled',
  },
  501: {
    tips: 'canceled by the facilitator',
    label: 'Canceled',
  },
  502: {
    tips: 'Minimal registration number not met',
    label: 'Canceled',
  },
  503: {
    tips: 'Product no longer available',
    label: 'Canceled',
  },
  504: {
    tips: 'Order processing failed',
    label: 'Canceled',
  },
  600: {
    tips: 'We are settling your payment',
    label: 'Paid',
  },
}

/**
 *
 * @param item
 * @returns {number}
 */
export const orderCalPrice = (item) => {
  let price = (item?.isPoint ? item?.point : item.price) || 0
  let refundPrice = 0

  if (item?.refund?.length > 0) {
    refundPrice = item?.refund.reduce((accumulator, currentItem) => {
      return accumulator + currentItem.amount
    }, 0)
  }

  return price - refundPrice
}
/**
 *
 * @param role
 */

export const isTeacherRole = (role) => !role?.includes('student')

/**
 *
 * @type {{cancel: string, booking: string, order: string, refund: string}}
 */

export const SERVICE_PACKAGE_USE = {
  cash: 'Purchase',
  booking: 'Session booked',
  cancel: 'Session canceled',
  refund: 'Refund processed',
  expired: 'Expired',
  teachingAccident: 'TeachingAccident',
  gift: 'Gift',
  point: 'Points exchange',
}

// claim point type
export const claimCategoryMap = {mentoring: 'service', correcting: 'correcting_service', premium: 'Premium content'}

/*
  service_premium
*/
export const calPremiumLecture = ({discountConfig, price, times}) => {
  const nowDate = new Date()
  const endDate = new Date(discountConfig?.end || nowDate.getTime() + 200)
  const diff = endDate.getTime() - nowDate.getTime()

  const hasDiscount = discountConfig?.enable && diff > 0
  const oriPrice = price * times
  return {
    hasDiscount,
    oriPrice,
    price: hasDiscount ? oriPrice * ((100 - discountConfig?.discount) / 100) : oriPrice,
  }
}

export const calPremiumMentor = ({count, servicePack, discount = 0}) => {
  const {price, discountConfig} = servicePack

  let choiceItem = {}
  const itemList = servicePack?.discount?.sort((a, b) => a.count - b.count) || []

  if (count < itemList?.[0]?.count) {
    choiceItem = {
      count: +count,
      discount: 0,
      gifts: 0,
    }
  } else {
    choiceItem = itemList.findLast((val) => val.count <= count) ?? itemList[itemList.length - 1]
  }

  const nowDate = new Date()
  const endDate = new Date(discountConfig?.end || nowDate.getTime() + 200)
  const diff = endDate.getTime() - nowDate.getTime()

  const rate = (price * (100 - choiceItem?.discount)) / 100
  const oriPrice = rate * count
  const gifts = choiceItem?.gifts || 0

  return {
    hasDiscount: 100 - discount > 0,
    oriPrice,
    rate,
    price: oriPrice * ((100 - discount) / 100),
    discount: choiceItem?.discount,
    count,
    gifts,
    totalCount: count + gifts,
  }
}

// 取最后5位
export const formatVoucherName = (name) => name.slice(-5)
