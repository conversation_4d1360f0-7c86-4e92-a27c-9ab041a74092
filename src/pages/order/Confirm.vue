<template>
  <q-layout view="hHh LpR fFf" class="bg-grey-3">
    <q-header class="bg-white text-black" elevated>
      <q-toolbar class="">
        <q-btn dense flat icon="navigate_before" round @click="goBack"></q-btn>
        <div class="col text-center">Confirm the order</div>
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm ">
      <q-page class="">
        <div class="q-px-md q-py-md q-pb-xl">
          <template v-if="type === 'service_substitute'">
            <Substitute ref="childRef" @updateData="updateData" />
          </template>
          <template v-else-if="type === 'prompt'">
            <Prompt ref="childRef" @updateData="updateData" />
          </template>
          <template v-else-if="type === 'service'">
            <Service ref="childRef" @updateData="updateData" />
          </template>
          <template v-else-if="type === 'service_premium'">
            <ServicePremium ref="childRef" @updateData="updateData" />
          </template>
          <template v-else>
            <Main ref="childRef" @updateData="updateData" />
          </template>
        </div>
        <div class="row justify-between btn-box q-pa-sm bg-white full-width fixed-bottom">
          <div class="pc-sm text-right">
            <template v-if="serviceSubstituteDetail?.isOnCampus">
              <CheckTea :detail="serviceSubstituteDetail" :chooseCity="chooseCity" />
            </template>
            <span v-if="isPointMode" class="q-mr-xs">
              <IconPoint :num="totalPoint" />
            </span>
            <span v-else class="text-primary text-bold q-mr-xs"> ${{ (totalPrice / 100).toFixed(2) }} </span>
            <q-btn class="q-mr-sm" color="primary"  label="Submit order" no-caps rounded size=""
              @click="checkout"></q-btn>
          </div>
        </div>

        <q-dialog ref="dialogRef" v-model="dialogShow" persistent class="bg-grey">
          <q-card class="column q-py-lg q-px-md bg-white" style="width: 460px">
            <div class="text-h6">The products/sessions are no longer available</div>
            <q-btn class="full-width q-mt-md" color="primary" label="I got it" @click="toBackBefore" no-caps rounded />
          </q-card>
        </q-dialog>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import { inject, nextTick, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { pubStore } from 'stores/pub'

const pub = pubStore()

import { isTeacherRole } from './consts.js'

import Main from './ConfirmModules/Index.vue'
import Substitute from './ConfirmModules/Substitute.vue'
import CheckTea from './ConfirmModules/CheckTea.vue'
import Prompt from './ConfirmModules/Prompt.vue'
import Service from './ConfirmModules/Service.vue'
import ServicePremium from './ConfirmModules/ServicePremium.vue'

const route = useRoute()
const router = useRouter()
const ids = ref(route.params.ids.split(','))
const type = ref(route.params.type)
const dialogShow = ref(false)

const serviceSubstituteDetail = ref(null)
const chooseCity = ref(null)
const totalPrice = ref(0)
const totalPoint = ref(0)

import { pointStore } from 'stores/point'


const childRef = ref(null)

const pStore = pointStore()

const isPointMode = ref(!!route?.query?.isPointMode)
const sharedSchool = ref(route?.query?.sharedSchool)


const isTeacher = ref(isTeacherRole(pub?.user?.roles))


console.log('pub', pub.user)


const goBack = (hash) => {
  router.go(-1)
}

const toBackBefore = () => {
  const homePage = isTeacher.value ? '/home/<USER>' : '/study/index'
  let path = route.query.back || homePage
  router.replace(path)
}

const checkout = async () => {
  console.log('checkout')
  const data = await childRef.value?.checkoutData()
  console.log('data', data)

  if(!data){
    return
  }

  console.log('data', data)
  // return

  $q.loading.show()
  // setTimeout(() => {
  //   $q.loading.hide()
  // }, 3000)

  await App.service('order')
    .get('checkLinks', {
      query: data?.linksQuery,
    })
    .then(async (res) => {
      if (res?.unpaidOrderId?.length > 0) {
        router.push({path: `/order/detail/${res?.unpaidOrderId?.[0]}`})
        return
      }
      if (res.links.length === res.available.length && res.available.length > 0) {
        await App.service('order')
          .create(data?.orderQuery)
          .then((rs) => {
            $q.notify({ type: 'positive', message: 'CreateOrder successfully' })
            console.log('create order', rs)
            if (rs.status === 200) {
              router.push({
                path: `/order/payResult/${rs._id}`,
                query: {
                  payBack: route?.query?.payBack,
                },
              })
            } else {
              router.replace({
                path: `/order/payConfirm/${rs._id}`,
                query: {
                  back: route?.query?.back,
                  payBack: route?.query?.payBack,
                },
              })
            }
          })
          .catch((err) => {
            $q.notify({ type: 'negative', message: err.message })
          })
      } else if (res.links.length > res.available.length && res.available.length > 0) {
        $q.notify({
          type: 'negative',
          message: 'The payment has been updated because some products are no longer available',
        })
        childInitData()
      } else if (res.available.length === 0) {
        dialogShow.value = true
      }
    })
    .catch((err) => {
      $q.notify({ type: 'negative', message: err.message })
      childInitData()
    })

  $q.loading.hide()
}


const childInitData = () => {
  childRef.value?.initData()
}

const updateData = (data = {}) => {
  console.log('updateData', data)
  totalPrice.value = data.totalPrice
  totalPoint.value = data.totalPoint
  dialogShow.value = data.dialogShow
  if (data.isSubstitute) {
    serviceSubstituteDetail.value = data.detail
    chooseCity.value = data.chooseCity
  }
}

const main = async () => {
  console.log('main')
}
onMounted(main)
</script>
