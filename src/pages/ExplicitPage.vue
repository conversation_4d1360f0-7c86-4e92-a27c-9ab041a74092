<template>
  <q-layout view="hHh LpR fFf">
    <PubHeader v-if="!isUnavailable" :showShare="canShareOrBookmark.share" isShareDetail />
    <q-page-container class="explicit-page-container bg-grey-1">
      <q-page class="column overflow-hidden" v-if="loading">
        <div class="col text-center q-pa-xl text-grey" v-if="!isUnavailable">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
      </q-page>
      <template v-else-if="!loading && content">
        <div v-if="course?.start" class="hidden">
          <!-- Do NOT REMOVE SessionTime <start>-->
          <SessionTime :session="course" @loaded="onCourseTimeLoaded" @changed="onCourseStartTimeChanged"></SessionTime>
          <SessionTime :session="course" deadline @expired="onCourseRegDeadlineExpired"></SessionTime>
          <!-- DO NOT REMOVE SessionTime <e n d>-->
        </div>
        <q-page v-if="isStudent" class="student-theme">
          <div class="q-pa-md student-bg rounded-borders-bottom-left-right-lg">
            <div class="pc-body q-py-sm">
              <div class="row q-col-gutter-md">
                <div class="col-xs-12 col-sm-6">
                  <q-img
                    spinner-color="white"
                    fit="cover"
                    class="fit rounded-borders-md"
                    :ratio="16 / 8"
                    :src="hashToUrl(content?.cover || serviceBooking?.servicePackUser?.snapshot?.cover) || '/v2/img/avatar.png'">
                    <div class="absolute-bottom-left text-subtitle2">
                      <q-avatar size="1.8rem" :color="attributes.color" text-color="white" :icon="attributes.icon" />
                    </div>
                  </q-img>
                </div>
                <div class="col-xs-12 col-sm-6 text-h4 text-weight-medium column">
                  <div class="col">
                    <div class="row no-wrap items-start">
                      <div class="col-auto row items-center">
                        <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
                        <q-icon v-if="(coursession?.premium && !coursession?.booking) || isPremiumContent" size="24px" name="svguse:/v2/icons.svg#premium">
                          <q-tooltip>premium workshop</q-tooltip>
                        </q-icon>
                      </div>
                      <div class="col q-pl-sm">
                        {{ content?.name || serviceBooking?.servicePackUser?.snapshot?.name || 'Untitled' }}
                      </div>
                      <div class="col-auto row items-center">
                        <!--
                        <q-icon v-if="isContents && content?.premiumAuth" class="q-pb-md" style="color: #ffcb45" size="14px" name="o_workspace_premium" />
                        -->
                        <q-icon v-if="coursession?.promotion" size="24px" name="svguse:/v2/icons.svg#promotion">
                          <q-tooltip>promotional premium workshop</q-tooltip>
                        </q-icon>
                        <SessionStatus :status="coursessionStatus" v-if="coursessionStatus"></SessionStatus>
                      </div>
                    </div>
                    <div class="row justify-between items-center">
                      <div v-if="!route.query.times">
                        <OwnerBox
                          :updatedAt="coursession?.createdAt || content?.createdAt"
                          :owner="!isCreator && isSelfStudy ? OfficialOwner : coursession?.owner || content?.owner" />
                      </div>
                      <SessionPrice
                        v-if="!isCoursePage && !isContents && !coursession?.booking && !isLimit"
                        :session="coursession || content"
                        :attributes="attributes"
                        :isPointMode="isPointMode"
                        :discountFinished="discountFinished"
                        :is-promotion="coursession?.promotion"></SessionPrice>
                    </div>
                    <div class="row q-mt-md items-center text-subtitle1" v-if="coursession?.substituteTeacherStatus === 1">
                      <div class="col">
                        <PubAvatar size="1.5rem" :src="coursession?.substituteTeacherInfo?.avatar" />
                        <span class="q-mx-md">{{ coursession?.substituteTeacherInfo?.nickname }}</span>
                      </div>
                      <q-badge class="q-pa-xs">Substitute</q-badge>
                    </div>
                  </div>
                  <template v-for="(item, index) in listItems" :key="index">
                    <div class="q-pt-sm full-width row" v-if="item.button && item.show">
                      <q-btn
                        class="col"
                        :disable="item.disable"
                        :loading="item.loading"
                        :push="!item.unelevated"
                        :unelevated="item.unelevated"
                        size="2.1rem"
                        :color="item.unelevated ? 'grey-11' : 'teal-1'"
                        text-color="black"
                        rounded
                        no-caps
                        @click="item.fn(coursession || content)">
                        <span class="q-pl-sm text-bold"> {{ item.label }}</span>
                      </q-btn>
                      <q-tooltip v-if="item.disable && item.tips" max-width="300px">{{ item.tips }}</q-tooltip>
                    </div>
                  </template>
                </div>
              </div>
              <div class="row q-pt-md justify-center">
                <div class="row hiden" v-if="route.query.times">
                  <div>No of sessions required to complete this course</div>
                  <div class="q-pl-xl">{{ route.query.times }}</div>
                </div>
                <div
                  class="col-xs-12 col-sm-6"
                  v-else-if="calculateDuration() && ((coursession && coursessionStatus == 'Scheduled' && sessionTimeState == 'start') || !coursession)">
                  <div class="row justify-center q-py-sm text-black">
                    <div class="items-center flex" :class="{column: $q.screen.lt.sm}">
                      <q-icon name="o_timer" size="lg" color="teal-1"></q-icon>
                      <div class="text-h6 q-pl-xs">
                        {{ calculateDuration() }}
                      </div>
                    </div>
                  </div>
                </div>
                <template v-if="coursession">
                  <div class="col-xs-12 col-sm-6" v-if="attributes.self">
                    <div class="row justify-center q-py-sm text-black">
                      <div class="items-center flex" :class="{column: $q.screen.lt.sm}">
                        <q-icon name="o_people" size="lg" color="teal-1"></q-icon>
                        <div class="text-h6">
                          <span class="q-px-sm"> Enrolled:</span>
                          {{ coursession.regNum }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <template v-else>
                    <div class="col-xs-12 col-sm-6 q-py-sm text-h6">
                      <SessionTime
                        justify-center
                        icon-size="lg"
                        icon-color="teal-1"
                        :column="$q.screen.lt.sm"
                        :student="isStudent"
                        :session="coursession"
                        @loaded="onSessionTimeLoaded"
                        @changed="onStartTimeChanged" />
                    </div>
                    <template v-if="attributes.enroll">
                      <div class="col-xs-12 col-sm-6 q-py-sm text-h6 row items-center" v-show="!isCoursePage && (isCreator || !isEnrolled)">
                        <SessionTime
                          justify-center
                          icon-size="lg"
                          icon-color="teal-1"
                          :column="$q.screen.lt.sm"
                          :session="coursession"
                          deadline
                          @loaded="onRegDeadlineLoaded"
                          @expired="onRegDeadlineExpired" />
                        <q-icon v-if="coursession?.discount?.size" class="q-ml-sm" name="warning_amber" color="red" size="sm">
                          <q-tooltip max-width="300px">Minimal number hasn't reached!</q-tooltip>
                        </q-icon>
                      </div>
                    </template>
                  </template>
                </template>
              </div>
              <div class="row justify-center">
                <div class="col-xs-12 col-md-9 row justify-center">
                  <template v-for="(item, index) in listItems" :key="index">
                    <div class="col-xs-6 col-sm-3 q-pt-md text-center" v-if="!item.button && item.show && item.kidIcon">
                      <q-btn
                        :icon="`svguse:/v2/icons.svg#kid-${item.kidIcon}`"
                        dense
                        rounded
                        unelevated
                        size="4.5rem"
                        color="teal-1"
                        @click="item.fn(coursession || content)"></q-btn>
                      <div class="q-pt-sm text-h6">{{ item.label }}</div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
          <div class="pc-body q-pa-md">
            <q-card class="rounded-borders-md q-mt-md">
              <q-card-section>
                <div class="text-h5 text-weight-bold">Class detail</div>
              </q-card-section>
              <q-card-section>
                <div class="text-subtitle1 text-weight-medium">Class tags</div>
                <PackageChips v-if="serviceBooking?.servicePackUser?.snapshot" is-session :pack="serviceBooking.servicePackUser.snapshot"></PackageChips>
                <SessionChips
                  v-else-if="coursession || content"
                  :session="coursession || content"
                  :isMyPublished="isMyPublished"
                  :isMyContents="isContents"
                  :isLibrary="isLib"></SessionChips>
                <div class="q-pt-sm" v-if="showServicePackInfo">This mentoring package is included in premium workshop package</div>
                <!--
                <div :class="`${$q.screen.lt.sm ? 'ellipsis-2-lines' : 'ellipsis-1-line'}`">
                  <q-chip :ripple="false" class="text-weight-medium" size="12px" color="teal-1" text-color="primary" v-if="!isContents && theServiceType">
                    {{ theServiceType }}
                  </q-chip>
                  <SubjectChip notips primary :subjects="subjects" />
                  <q-chip :ripple="false" class="text-weight-medium" size="12px" color="teal-1" text-color="primary" v-if="isContents && contentsCount">
                    {{ contentsCount }}
                  </q-chip>
                  <q-chip :ripple="false" class="text-weight-medium" size="12px" color="teal-1" text-color="primary" v-if="!isContents && sessionsCount">
                    {{ sessionsCount }}
                  </q-chip>
                  <q-chip :ripple="false" class="text-weight-medium" size="12px" color="teal-1" text-color="primary" v-if="coursession?.unitType">
                    {{ coursession.unitType }}
                  </q-chip>
                  <div class="q-pt-sm" v-if="showServicePackInfo">This mentoring package is included in premium workshop package</div>
                </div>
                  -->
              </q-card-section>
              <template v-for="(item, index) in listItems" :key="index">
                <q-item
                  v-if="item.show && (item.evaluation || item.assess)"
                  :clickable="!item.disable && !item.assess"
                  :v-ripple="!item.disable"
                  :disable="item.disable"
                  @click="item.fn(coursession || content)"
                  :class="{'assessing-status': item.assess}">
                  <q-item-section>
                    <q-item-label lines="1">
                      <div class="text-subtitle1 text-weight-medium">
                        {{ item.label }}
                      </div>
                    </q-item-label>
                    <q-item-label lines="1">
                      <template v-if="item.assess">
                        <div class="debug hidden">
                          <pre>isCreator:{{ isCreator }}</pre>
                          <pre>item :{{ item }}</pre>
                          <pre>coursession._id:{{ coursession._id }}</pre>
                          <pre>pub.user._id:{{ pub.user._id }}</pre>
                          <pre>toolCount:{{ coursession.task?.toolCount }}</pre>
                          <pre>toolStat:{{ coursession.toolStat }}</pre>
                        </div>
                        <div v-if="item.self" class="q-ml-md" :class="{'gray-filter': !coursession.task?.toolCount?.self}">
                          <PersonAvatar
                            :tagText="getTextAndChecked(coursession, 'self')['text']"
                            :isChecked="getTextAndChecked(coursession, 'self')['checked']"
                            src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                        </div>
                        <div class="row q-gutter-md justify-start q-pl-md" v-else-if="item.all">
                          <div v-if="coursession.task?.toolCount?.self" :class="{'gray-filter': avatarDisabled('self')}">
                            <PersonAvatar
                              :tagText="getTextAndChecked(coursession, 'self')['text']"
                              :isChecked="getTextAndChecked(coursession, 'self')['checked']"
                              src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                          </div>
                          <div v-if="coursession.task?.toolCount?.others" :class="{'gray-filter': avatarDisabled('others')}">
                            <PersonAvatar
                              :tagText="getTextAndChecked(coursession, 'others')['text']"
                              :isChecked="getTextAndChecked(coursession, 'others')['checked']"
                              src="/v2/img/assessment-tool/peer-avatar.png"></PersonAvatar>
                          </div>
                          <div :class="{'gray-filter': avatarDisabled('teacher')}" v-if="coursession.task?.toolCount?.teacher && !isStudent && isCreator">
                            <PersonAvatar
                              :tagText="getTextAndChecked(coursession, 'teacher')['text']"
                              :isChecked="getTextAndChecked(coursession, 'teacher')['checked']"
                              src="/v2/img/assessment-tool/teacher-avatar.png"></PersonAvatar>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        <q-chip
                          class="q-mr-sm"
                          :ripple="false"
                          :label="coursession?.rating ? 'Completed' : 'Incompleted'"
                          :color="coursession?.rating ? 'green-1' : 'red-1'"
                          :text-color="coursession?.rating ? 'green' : 'red'"></q-chip>
                        <span v-if="coursession?.rating"> {{ evaluationRes?.message }}</span>
                        <span v-else class="text-grey-7"> No review </span>
                      </template>
                    </q-item-label>
                  </q-item-section>

                  <q-item-section side v-if="!item.assess">
                    <q-spinner-ball v-if="item.evaluation && item.loading" color="primary" size="sm" />
                    <q-icon v-else name="navigate_next" />
                  </q-item-section>
                  <q-tooltip v-if="item.disable && item.tips" max-width="300px">{{ item.tips }}</q-tooltip>
                </q-item>
              </template>
            </q-card>
            <template v-if="isTaskMode">
              <template v-for="(group, index) in groups" :key="index">
                <q-expansion-item v-if="getLinksByGroup(group).length" group="somegroup" class="bg-white overflow-hidden shadow-2 rounded-borders-md q-my-md">
                  <template v-slot:header>
                    <q-item-section>
                      <div class="row">
                        <div class="col text-subtitle1 text-weight-medium">{{ group.name }}</div>
                        <div class="row items-center">
                          <span class="q-pl-sm text-grey">
                            {{ getLinksByGroup(group).length }} {{ isContents ? 'Content' : 'Lesson' }}{{ getLinksByGroup(group).length > 1 ? 's' : '' }}
                          </span>
                        </div>
                      </div>
                    </q-item-section>
                  </template>
                  <NoData v-if="!getLinksByGroup(group)?.length" size="9rem"></NoData>
                  <div class="overflow-hidden" v-for="(item, idx) in getLinksByGroup(group)" :key="idx">
                    <q-item>
                      <q-item-section side>
                        <q-avatar size="1.8rem" :color="getAttributes(item, 'color')" text-color="white" :icon="getAttributes(item, 'icon')" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label lines="1"
                          >{{ item.name ? item.name : 'Untitled' }}
                          <q-chip :ripple="false" v-if="item.category" size="sm" :label="item.category" :color="item.color" class="text-white" square></q-chip>
                        </q-item-label>
                        <q-item-label lines="1" caption v-if="!attributes.content">
                          <SessionTime :session="item" :student="!isCreator" @loaded="onChildSessionTimeLoaded(val, item)"></SessionTime>
                        </q-item-label>
                        <q-item-label lines="1" caption v-if="calculateDuration(item) && item._status !== 'Ongoing' && getItemStatus(item) == 'Scheduled'">{{
                          calculateDuration(item)
                        }}</q-item-label>
                      </q-item-section>
                    </q-item>
                    <div class="q-pa-md rounded-borders-md row items-center q-col-gutter-md">
                      <template v-for="(action, i) in getActionsByItem(item)" :key="i">
                        <div
                          v-if="action.avatar"
                          class="col-xs-12 col-sm-3 col-md-3 explicit-page-min-height items-center flex"
                          :class="$q.screen.lt.sm ? 'justify-end' : 'justify-start'">
                          <div v-if="action.self" class="q-mr-md" :class="{'gray-filter': !item.task?.toolCount?.self}">
                            <PersonAvatar
                              :tagText="getTextAndChecked(item, 'self')['text']"
                              :isChecked="getTextAndChecked(item, 'self')['checked']"
                              src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                          </div>
                          <div class="row q-gutter-md justify-center" v-else-if="action.all">
                            <div v-if="item.task?.toolCount?.self" :class="{'gray-filter': avatarDisabled('self')}">
                              <PersonAvatar
                                :tagText="getTextAndChecked(item, 'self')['text']"
                                :isChecked="getTextAndChecked(item, 'self')['checked']"
                                src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                            </div>
                            <div v-if="item.task?.toolCount?.others" :class="{'gray-filter': avatarDisabled('others')}">
                              <PersonAvatar
                                :tagText="getTextAndChecked(item, 'others')['text']"
                                :isChecked="getTextAndChecked(item, 'others')['checked']"
                                src="/v2/img/assessment-tool/peer-avatar.png"></PersonAvatar>
                            </div>
                            <div :class="{'gray-filter': avatarDisabled('teacher')}" v-if="item.task?.toolCount?.teacher && !isStudent && isCreator">
                              <PersonAvatar
                                :tagText="getTextAndChecked(item, 'teacher')['text']"
                                :isChecked="getTextAndChecked(item, 'teacher')['checked']"
                                src="/v2/img/assessment-tool/teacher-avatar.png"></PersonAvatar>
                            </div>
                          </div>
                        </div>
                        <div
                          v-else-if="action.show"
                          class="col-xs-12 explicit-page-min-height"
                          :class="$q.screen.lt.sm ? '' : action.kidIcon ? 'col-sm-4 col-md-3' : 'col-sm-4'">
                          <q-btn
                            class="fit rounded-borders-lg q-py-md"
                            text-color="black"
                            size="lg"
                            :unelevated="!!action.kidIcon"
                            :align="$q.screen.lt.sm && action.kidIcon ? 'left' : 'center'"
                            :color="action.push ? 'teal-3' : 'teal-1'"
                            :push="action.push"
                            :disable="action.disable"
                            @click="action.fn(item)"
                            no-caps>
                            <div class="row q-gutter-md">
                              <q-icon v-if="action.kidIcon" :name="`svguse:/v2/icons.svg#kid-${action.kidIcon}`"></q-icon>
                              <div>{{ action.label }}</div>
                            </div>
                          </q-btn>
                          <q-tooltip v-if="action.tips && action.disable" max-width="300px">{{ action.tips }}</q-tooltip>
                        </div>
                      </template>
                    </div>
                  </div>
                </q-expansion-item>
              </template>
            </template>
            <template v-else>
              <template v-if="isLimit && !isVideoMode">
                <q-expansion-item class="bg-white shadow-2 overflow-hidden rounded-borders-md q-my-md" v-if="!attributes?.tool">
                  <template v-slot:header>
                    <q-item-section>
                      <div class="row">
                        <div class="col text-subtitle1 text-weight-medium">Link contents</div>
                        <div class="row items-center">
                          <span class="q-pl-sm text-grey">
                            {{ uniqueList.length }} {{ isContents ? 'Content' : 'Lesson' }}{{ uniqueList.length > 1 ? 's' : '' }}
                          </span>
                        </div>
                      </div>
                    </q-item-section>
                  </template>
                  <template v-for="(link, index) in uniqueList" :key="index">
                    <q-separator />
                    <SessionBoard
                      @click="onLinkItemClick"
                      :isLibrary="isLib"
                      :isMyContent="isContents"
                      isPreviewPage
                      :isLimit="isLimit"
                      :isSysView="isSysView"
                      :isVerify="isVerify"
                      :authId="route.query.authId"
                      :spuid="route.query.spuid"
                      no-border
                      :no-avatar="!!route.query.times || isSysView"
                      :no-price="isLib"
                      :courseId="attributes.course ? id : null"
                      :session="link"></SessionBoard>
                  </template>
                  <NoData v-if="!uniqueList.length" size="9rem"></NoData>
                </q-expansion-item>
              </template>
              <template v-else>
                <q-expansion-item
                  v-for="(group, index) in groups"
                  :key="index"
                  group="somegroup"
                  class="bg-white shadow-2 overflow-hidden rounded-borders-md q-my-md">
                  <template v-slot:header>
                    <q-item-section>
                      <div class="row">
                        <div class="col text-subtitle1 text-weight-medium">{{ group.name }}</div>
                        <div class="row items-center">
                          <span class="q-pl-sm text-grey">
                            {{ getMainLinksByGroup(group).length }} {{ isContents ? 'Content' : 'Lesson'
                            }}{{ getMainLinksByGroup(group).length > 1 ? 's' : '' }}
                          </span>
                        </div>
                      </div>
                    </q-item-section>
                  </template>
                  <template v-for="(link, index) in getMainLinksByGroup(group)" :key="index">
                    <q-separator />
                    <SessionBoard
                      @click="onLinkItemClick"
                      :isLibrary="isLib"
                      :isMyContent="isContents"
                      isPreviewPage
                      :isLimit="isLimit"
                      no-border
                      :no-price="isLib"
                      :courseId="attributes.course ? id : null"
                      :session="link"></SessionBoard>
                  </template>
                  <NoData v-if="!getMainLinksByGroup(group).length" size="9rem"></NoData>
                </q-expansion-item>
              </template>
            </template>
            <q-card class="rounded-borders-md" v-if="!Acan.isEmpty(relevant)">
              <q-card-section>
                <div class="text-subtitle1 text-weight-medium">Relevant content</div>
              </q-card-section>
              <SessionBoard :session="relevant" isRelevant></SessionBoard>
            </q-card>
            <q-card
              class="rounded-borders-md q-mt-md"
              v-if="
                showServicePackInfo &&
                !route.query?.back?.includes('/detail/booked') &&
                !route.query?.back?.includes('/service/pack') &&
                !orderedService.includes(servicePack?._id)
              ">
              <q-card-section>
                <div class="text-subtitle1 text-weight-medium">Bundled Mentoring service package</div>
              </q-card-section>
              <PackageCard
                no-price
                :pack="servicePack"
                :pack-user="servicePackUser"
                :is-creator="isCreator"
                :is-promotion="coursession.promotion"
                :is-educator="false"></PackageCard>
            </q-card>
          </div>
        </q-page>
        <q-page v-else class="pc-sm"
          ><!--For teachers-->
          <div class="q-pa-md">
            <BreadCrumbs title="Preview" v-if="showNav"></BreadCrumbs>
            <div class="row no-wrap items-start text-h5 text-weight-medium">
              <div class="col-auto row items-center">
                <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack" v-if="showNav || route.query.back"></q-btn>
                <q-icon v-if="(coursession?.premium && !coursession?.booking) || isPremiumContent" size="24px" name="svguse:/v2/icons.svg#premium">
                  <q-tooltip>premium workshop</q-tooltip>
                </q-icon>
              </div>
              <div class="col q-pa-sm">
                {{ content?.name || serviceBooking?.servicePackUser?.snapshot?.name || 'Untitled' }}
              </div>
              <div class="col-auto row items-center q-pt-sm">
                <!--
                <q-icon v-if="isContents && content?.premiumAuth" class="q-pb-md" style="color: #ffcb45" size="14px" name="o_workspace_premium" />
                -->
                <q-icon v-if="coursession?.promotion" size="24px" name="svguse:/v2/icons.svg#promotion">
                  <q-tooltip>promotional premium workshop</q-tooltip>
                </q-icon>
                <div v-if="isContents && content?.orderId">
                  <q-btn class="q-mx-xs" round size="sm" text-color="grey-8" icon="o_shopping_bag"></q-btn>
                </div>
                <SessionStatus :status="coursessionStatus" v-if="coursessionStatus"></SessionStatus>
                <div v-if="isLib && !isCoursePage && !isContents && !coursession?.booking && !isLimit">
                  <div class="text-right">
                    <SessionPrice
                      :session="coursession || content"
                      :isPointMode="isPointMode"
                      :attributes="attributes"
                      :discountFinished="discountFinished"
                      :is-promotion="coursession?.promotion"></SessionPrice>
                  </div>
                  <div class="text-primary text-caption" v-if="content?.discount?.end && !discountFinished">
                    <SessionTime :session="content" discount published @finished="onDiscountFinished" />
                  </div>
                </div>
              </div>
            </div>
            <div class="row col q-col-gutter-md" v-if="!route.query.times && !route.query?.hideAuthor">
              <div class="col-xs-12 col-sm-5">
                <div class="text-weight-medium" v-if="isVerifyOrSysView && serviceAuth?._id">Author</div>
                <OwnerBox
                  :updatedAt="
                    isVerifyOrSysView && serviceAuth?.approval?.submitted
                      ? serviceAuth.approval.submitted
                      : isLib
                        ? content?.publish?.date
                        : serviceAuth?.status === 0
                          ? ''
                          : coursession?.createdAt || content?.createdAt
                  "
                  :prepend="isVerifyOrSysView && serviceAuth?._id && serviceAuth?.status > 0 ? 'Submitted on' : ''"
                  :owner="
                    !isCreator && isSelfStudy
                      ? OfficialOwner
                      : coursession?.owner
                        ? {...coursession?.owner, showFullName: coursession?.owner?._id === pub?.user?._id}
                        : {...content?.owner, showFullName: content?.owner?._id === pub?.user?._id}
                  " />
              </div>
              <div class="col-xs-12 col-sm-5" v-if="isVerifyOrSysView && auditor?._id">
                <div class="text-weight-medium">Auditor</div>
                <OwnerBox
                  :prepend="serviceAuth?.status == 1 ? 'Claimed on' : serviceAuth?.status == 2 ? 'Approved on' : 'Rejected on'"
                  :updatedAt="serviceAuth.approval?.approved"
                  :owner="{...auditor, showFullName: pub?.user?._id === auditor?._id}" />
              </div>
            </div>
            <InviteBtn v-if="coursessionStatus" :detail="coursession" @cb="main" />
            <div class="q-my-md">
              <q-img
                spinner-color="white"
                fit="cover"
                class="fit rounded-borders-md"
                :ratio="16 / 7"
                :src="hashToUrl(content?.cover || serviceBooking?.servicePackUser?.snapshot?.cover) || '/v2/img/avatar.png'">
                <div class="absolute-bottom-left text-subtitle2">
                  <q-avatar size="1.8rem" :color="attributes.color" text-color="white" :icon="attributes.icon" />
                </div>
              </q-img>
            </div>
            <div class="q-mb-md row justify-between flex items-center">
              <!-- <div class="row col q-col-gutter-md" v-if="!route.query.times && !route.query?.hideAuthor">
                <div class="col-xs-12 col-sm-5">
                  <div class="text-weight-medium" v-if="isVerifyOrSysView && serviceAuth?._id">Author</div>
                  <OwnerBox
                    :updatedAt="
                      isVerifyOrSysView && serviceAuth?.approval?.submitted
                        ? serviceAuth.approval.submitted
                        : isLib
                        ? content?.publish?.date
                        : serviceAuth?.status === 0
                        ? ''
                        : coursession?.createdAt || content?.createdAt
                    "
                    :prepend="isVerifyOrSysView && serviceAuth?._id && serviceAuth?.status > 0 ? 'Submitted on' : ''"
                    :owner="!isCreator && isSelfStudy ? OfficialOwner : (coursession?.owner? {...coursession?.owner, showFullName: true} : {...content?.owner, showFullName: true})" />
                </div>
                <div class="col-xs-12 col-sm-5" v-if="isVerifyOrSysView && auditor?._id">
                  <div class="text-weight-medium">Auditor</div>
                  <OwnerBox
                    :prepend="serviceAuth?.status == 1 ? 'Claimed on' : serviceAuth?.status == 2 ? 'Approved on' : 'Rejected on'"
                    :updatedAt="serviceAuth?.status == 1 ? serviceAuth?.followedAt : serviceAuth.approval?.approved"
                    :owner="{...auditor, showFullName: true}" />
                </div>
              </div> -->
              <div class="row items-center">
                <q-btn v-if="republished" flat :loading="updating" @click="updateToLatestVersion" color="primary" rounded no-caps>
                  <div>
                    <q-icon name="o_file_download"> </q-icon>
                    <div>Latest version</div>
                  </div>
                  <q-tooltip>
                    <div>This content has been updated by its original</div>
                    <div>author. Click to update to the latest version.</div>
                    <div>Please notice that updating will replace the</div>
                    <div>current content and it is not reversible.</div>
                  </q-tooltip>
                </q-btn>
                <template v-if="route.query?.subform === 'subTeacher'">
                  <IncomePrice :item="coursession" />
                </template>
                <template v-else>
                  <SessionPrice
                    v-if="!isLib && !isCoursePage && !isContents && !coursession?.booking && !isLimit"
                    :session="coursession || content"
                    :isPointMode="isPointMode"
                    :attributes="attributes"
                    :discountFinished="discountFinished"
                    :is-promotion="coursession?.promotion"></SessionPrice>
                </template>
              </div>
            </div>
            <PackageChips v-if="serviceBooking?.servicePackUser?.snapshot" is-session :pack="serviceBooking.servicePackUser.snapshot"></PackageChips>
            <template v-else-if="coursession || content">
              <SessionChipsServiceAuth
                v-if="route.query.authId"
                :session="coursession || content"
                :isMyPublished="isMyPublished"
                :isMyContents="isContents"
                :isLibrary="isLib"
                :authStatus="serviceAuth.status"></SessionChipsServiceAuth>
              <SessionChips
                v-else
                :session="coursession || content"
                :isMyPublished="isMyPublished"
                :isMyContents="isContents"
                :isLibrary="isLib"
                :authStatus="serviceAuth.status"></SessionChips>
            </template>
            <div class="q-mb-md hidden">
              <q-chip :ripple="false" v-if="showServicePackInfo" size="12px" square color="teal-2" class="q-border-1-grey"
                >Mentoring 1V1 * {{ coursession.servicePack.times }}</q-chip
              >
              <q-chip :ripple="false" class="text-weight-medium" size="12px" color="teal-1" text-color="primary" v-if="!isContents && theServiceType">
                {{ theServiceType }}
              </q-chip>
              <SubjectChip notips :subjects="subjects" />
              <q-chip :ripple="false" class="text-weight-medium" size="12px" color="teal-1" text-color="primary" v-if="isContents && contentsCount">
                {{ contentsCount }}
              </q-chip>
              <q-chip :ripple="false" class="text-weight-medium" size="12px" color="teal-1" text-color="primary" v-if="!isContents && sessionsCount">
                {{ sessionsCount }}
              </q-chip>
              <!--
              <q-chip :ripple="false" class="text-weight-medium" size="12px" color="teal-1" text-color="primary" v-if="coursession?.unitType">
                {{ coursession.unitType }}
              </q-chip>
              -->
              <div class="q-pt-sm" v-if="showServicePackInfo">This mentoring package is included in premium workshop package</div>
            </div>
            <div class="row hidden" v-if="route.query.times">
              <div>No of sessions required to complete this course</div>
              <div class="q-pl-xl">{{ route.query.times }}</div>
            </div>
            <template v-else-if="calculateDuration()">
              <div
                class="row justify-between q-py-sm text-grey-7"
                v-if="(coursession && coursessionStatus == 'Scheduled' && sessionTimeState == 'start') || !coursession">
                <div class="items-center flex">
                  <q-icon name="o_timer" color="primary" size="xs" class="q-pr-sm"></q-icon>
                  {{ calculateDuration() }}
                </div>
              </div>
            </template>
            <template v-if="coursession">
              <template v-if="attributes.self">
                <div class="q-py-sm text-grey-7">Enrolled {{ coursession.regNum }}</div>
              </template>
              <template v-else>
                <div class="q-py-sm text-grey-7">
                  <SessionTime :session="coursession" @loaded="onSessionTimeLoaded" :hour="isPremiumLecture ? 2 : 12" @changed="onStartTimeChanged" />
                </div>
                <template v-if="attributes.enroll">
                  <div class="row q-py-sm text-grey-7" v-show="!isCoursePage && (isCreator || !isEnrolled)">
                    <SessionTime :session="coursession" deadline @loaded="onRegDeadlineLoaded" @expired="onRegDeadlineExpired" />
                    <div v-if="coursession && isCreator && !isCoursePage && coursession.sessionType == 'live'">
                      <div class="q-pl-sm" v-if="!coursession.regNum || coursession.regNum < coursession?.discount?.size">
                        <q-icon name="warning_amber" color="red" size="sm">
                          <q-tooltip max-width="300px">Minimal number hasn't reached!</q-tooltip>
                        </q-icon>
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="coursession && isCreator && !isCoursePage && coursession.sessionType == 'live'"
                    class="justify-between flex items-center text-grey-7">
                    <q-expansion-item
                      class="overflow-hidden full-width explicit-page-enrolled-data"
                      expand-icon-toggle
                      expand-separator
                      v-if="coursession.regNum && coursession.regNum >= coursession?.discount?.size">
                      <template v-slot:header>
                        <q-item-section avatar>
                          <q-icon color="grey-7" name="o_group" size="xs" />
                        </q-item-section>

                        <q-item-section> </q-item-section>

                        <q-item-section side>Enrolled {{ coursession.regNum }}/{{ coursession.regMax }} </q-item-section>
                      </template>

                      <div class="row q-py-sm" v-for="(o, i) in members" :key="i">
                        <PubAvatar size="1.2rem" :src="o.avatar" />
                        <div class="q-pl-sm">{{ nameFormatter(o) }}</div>
                      </div>
                    </q-expansion-item>
                  </div>
                </template>
              </template>
            </template>
          </div>
          <q-separator inset></q-separator>
          <q-list class="explicit-page-actions-list q-px-md">
            <q-expansion-item class="overflow-hidden" v-model="expanded" dense-toggle switch-toggle-side>
              <template v-for="(item, index) in listItems" :key="index">
                <template v-if="!studentId || (studentId && (item.overview || item.slides || item.studyData))">
                  <template v-if="item.button">
                    <q-item v-if="item.show">
                      <q-btn
                        class="col"
                        :disable="item.disable && !item.dialog"
                        :loading="item.loading"
                        outline
                        rounded
                        color="primary"
                        no-caps
                        @click="item.fn(coursession || content, item)">
                        <q-icon :name="item.icon" size="1.2rem"></q-icon>
                        <span class="q-pl-sm"> {{ item.label }}</span>
                      </q-btn>
                      <q-tooltip v-if="item.disable && item.tips" max-width="300px">{{ item.tips }}</q-tooltip>
                    </q-item>
                  </template>
                  <template v-else>
                    <q-item
                      v-if="item.show"
                      :clickable="!item.disable && !item.assess"
                      :v-ripple="!item.disable"
                      :disable="item.disable"
                      @click="item.fn(coursession || content)"
                      :class="{'assessing-status': item.assess}">
                      <q-item-section avatar>
                        <q-icon :color="isAccident && item.evaluation && isCreator && !accidentRes?.evidencesTeacher?.length ? 'red' : ''" :name="item.icon" />
                      </q-item-section>

                      <q-item-section>
                        <q-item-label lines="1">
                          <div class="row items-center">
                            <div>
                              {{ item.label }}
                            </div>
                            <template v-if="item.evaluation && !isCreator">
                              <q-space></q-space>
                              <div class="row q-pl-md">
                                <q-icon color="primary" name="done" size="sm" v-if="coursession?.rating"></q-icon>
                                <span v-else class="text-negative">Incomplete</span>
                              </div>
                            </template>
                            <template v-if="item.roster">
                              <div class="row q-pl-md text-primary">
                                <q-icon name="o_group" size="1rem"></q-icon>
                                <template v-if="attributes.booking"> 1 </template>
                                <template v-else-if="isCoursePage">
                                  {{ isMyClass ? course?.students?.length : course?.regNum }}
                                </template>
                                <template v-else>
                                  {{ isMyClass ? coursession?.students?.length : coursession?.regNum }}
                                </template>
                                <q-icon name="chevron_right" size="1rem" color="teal-2"></q-icon>
                                <q-icon name="o_task_alt" size="1rem"></q-icon>
                                {{ roomStat?.attend?.length ?? 0 }}
                                <q-icon name="remove" size="1rem" color="teal-2" class="rotate-90"></q-icon>
                                <q-icon name="o_person_off" size="1rem"></q-icon>
                                {{ roomStat?.block?.length ?? 0 }}
                              </div>
                            </template>
                            <template v-if="item.assess">
                              <div class="debug hidden">
                                <pre>isCreator:{{ isCreator }}</pre>
                                <pre>coursession._id:{{ coursession._id }}</pre>
                                <pre>pub.user._id:{{ pub.user._id }}</pre>
                                <pre>toolCount:{{ coursession.task?.toolCount }}</pre>
                                <pre>toolStat:{{ coursession.toolStat }}</pre>
                              </div>
                              <div v-if="item.self" class="q-ml-md" :class="{'gray-filter': !coursession.task?.toolCount?.self}">
                                <PersonAvatar
                                  :tagText="getTextAndChecked(coursession, 'self')['text']"
                                  :isChecked="getTextAndChecked(coursession, 'self')['checked']"
                                  src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                              </div>
                              <div class="row q-gutter-md justify-center q-pl-md" v-else-if="item.all">
                                <div v-if="coursession.task?.toolCount?.self" :class="{'gray-filter': avatarDisabled('self')}">
                                  <PersonAvatar
                                    :tagText="getTextAndChecked(coursession, 'self')['text']"
                                    :isChecked="getTextAndChecked(coursession, 'self')['checked']"
                                    src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                                </div>
                                <div v-if="coursession.task?.toolCount?.others" :class="{'gray-filter': avatarDisabled('others')}">
                                  <PersonAvatar
                                    :tagText="getTextAndChecked(coursession, 'others')['text']"
                                    :isChecked="getTextAndChecked(coursession, 'others')['checked']"
                                    src="/v2/img/assessment-tool/peer-avatar.png"></PersonAvatar>
                                </div>
                                <div :class="{'gray-filter': avatarDisabled('teacher')}" v-if="coursession.task?.toolCount?.teacher && !isStudent && isCreator">
                                  <PersonAvatar
                                    :tagText="getTextAndChecked(coursession, 'teacher')['text']"
                                    :isChecked="getTextAndChecked(coursession, 'teacher')['checked']"
                                    src="/v2/img/assessment-tool/teacher-avatar.png"></PersonAvatar>
                                </div>
                              </div>
                            </template>
                          </div>
                        </q-item-label>
                        <q-item-label lines="1" caption v-if="item.caption">{{ item.path }}{{ item.caption }}</q-item-label>
                        <q-item-label lines="1" v-if="takeaway?._id && item.takeaway && coursession?.count">
                          <div class="row text-grey-7 items-center">
                            <q-icon name="o_mark_email_read" color="green"></q-icon>
                            <div class="q-px-xs text-caption q-pr-sm">{{ coursession.count.report }}/{{ studentsCount }}</div>
                            <q-icon name="o_mark_email_read" color="red"></q-icon>
                            <div class="q-px-xs text-caption">{{ studentsCount - coursession.count?.report }}/{{ studentsCount }}</div>
                          </div>
                        </q-item-label>
                      </q-item-section>

                      <q-item-section side v-if="item.roster && coursession?.block">
                        <q-icon name="o_block" color="negative">
                          <q-tooltip max-width="260px">Participants will blocked once they end the session</q-tooltip>
                        </q-icon>
                      </q-item-section>
                      <q-item-section side v-if="item.takeaway && takeaway?._id">
                        <div class="row q-pl-md">
                          <q-chip
                            v-if="coursession?.count?.report && coursession?.count?.report === coursession?.count?.students"
                            color="green-2"
                            text-color="green"
                            square
                            :ripple="false"
                            >Complete</q-chip
                          >
                          <q-chip v-else color="red-2" text-color="red" square :ripple="false">Incomplete</q-chip>
                        </div>
                      </q-item-section>
                      <q-item-section side v-if="!item.assess">
                        <q-spinner-ball v-if="item.evaluation && item.loading" color="primary" size="sm" />
                        <q-icon v-else name="navigate_next" />
                      </q-item-section>
                      <q-tooltip v-if="item.disable && item.tips" max-width="300px">{{ item.tips }}</q-tooltip>
                    </q-item>
                    <!-- <template v-if="item.show && item.isCustom">
                      <InviteBtn v-if="item.type === 'substitute'" :detail="coursession" @cb="main" />
                    </template> -->
                  </template>
                </template>
              </template>
            </q-expansion-item>
            <q-item clickable v-ripple @click="expanded = !expanded" v-if="isCreator && !studentId && !isLimit">
              <q-item-section>
                <q-item-label class="row items-center justify-center">
                  <q-icon :name="expanded ? 'arrow_drop_up' : 'arrow_drop_down'" size="2em"></q-icon>
                  <div class="text-grey-7 q-pl-sm">{{ expanded ? 'Hide' : 'Show' }} Actions</div>
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
          <q-separator inset></q-separator>
          <q-list class="q-ma-md rounded-borders">
            <template v-if="isTaskMode">
              <template v-for="(group, index) in groups" :key="index">
                <q-expansion-item v-if="getLinksByGroup(group).length" group="somegroup" class="overflow-hidden q-border-1 rounded-borders-md q-mb-md">
                  <template v-slot:header>
                    <q-item-section>
                      <div class="row">
                        <div class="col">{{ group.name }}</div>
                        <div class="row items-center">
                          <q-icon name="dynamic_feed" color="grey" size="1rem" />
                          <span class="q-pl-sm text-grey">
                            {{ getLinksByGroup(group).length }} {{ isContents ? 'Content' : 'Lesson' }}{{ getLinksByGroup(group).length > 1 ? 's' : '' }}
                          </span>
                        </div>
                      </div>
                    </q-item-section>
                  </template>
                  <NoData v-if="!getLinksByGroup(group)?.length" size="9rem"></NoData>
                  <q-expansion-item class="overflow-hidden" :group="group._id" icon="perm_identity" v-for="(item, idx) in getLinksByGroup(group)" :key="idx">
                    <template v-slot:header>
                      <q-item-section avatar>
                        <q-avatar size="1.8rem" :color="getAttributes(item, 'color')" text-color="white" :icon="getAttributes(item, 'icon')" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label lines="1"
                          >{{ item.name ? item.name : 'Untitled' }}
                          <q-chip :ripple="false" v-if="item.category" size="sm" :label="item.category" :color="item.color" class="text-white" square></q-chip>
                        </q-item-label>
                        <q-item-label lines="1" caption v-if="!attributes.content">
                          <SessionTime :session="item" :student="!isCreator" @loaded="onChildSessionTimeLoaded(val, item)"></SessionTime>
                        </q-item-label>
                        <q-item-label lines="1" caption v-if="calculateDuration(item) && item._status !== 'Ongoing' && getItemStatus(item) == 'Scheduled'">{{
                          calculateDuration(item)
                        }}</q-item-label>
                      </q-item-section>
                      <q-item-section side v-if="item._status">
                        <q-badge color="primary" :label="item._status" />
                      </q-item-section>
                    </template>
                    <div class="q-pa-md q-border-1 rounded-borders-md row items-center q-ma-md">
                      <template v-for="(action, i) in getActionsByItem(item)" :key="i">
                        <template v-if="!studentId || (studentId && (action.overview || action.slides || action.studyData))">
                          <div v-if="action.avatar" class="col-xs-4 col-sm-3 col-md-3 explicit-page-min-height items-center flex justify-center">
                            <div v-if="action.self" class="q-mr-md" :class="{'gray-filter': !item.task?.toolCount?.self}">
                              <PersonAvatar
                                :tagText="getTextAndChecked(item, 'self')['text']"
                                :isChecked="getTextAndChecked(item, 'self')['checked']"
                                src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                            </div>
                            <div class="row q-gutter-md justify-center" v-else-if="action.all">
                              <div v-if="item.task?.toolCount?.self" :class="{'gray-filter': avatarDisabled('self')}">
                                <PersonAvatar
                                  :tagText="getTextAndChecked(item, 'self')['text']"
                                  :isChecked="getTextAndChecked(item, 'self')['checked']"
                                  src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                              </div>
                              <div v-if="item.task?.toolCount?.others" :class="{'gray-filter': avatarDisabled('others')}">
                                <PersonAvatar
                                  :tagText="getTextAndChecked(item, 'others')['text']"
                                  :isChecked="getTextAndChecked(item, 'others')['checked']"
                                  src="/v2/img/assessment-tool/peer-avatar.png"></PersonAvatar>
                              </div>
                              <div :class="{'gray-filter': avatarDisabled('teacher')}" v-if="item.task?.toolCount?.teacher && !isStudent && isCreator">
                                <PersonAvatar
                                  :tagText="getTextAndChecked(item, 'teacher')['text']"
                                  :isChecked="getTextAndChecked(item, 'teacher')['checked']"
                                  src="/v2/img/assessment-tool/teacher-avatar.png"></PersonAvatar>
                              </div>
                            </div>
                          </div>
                          <div v-else-if="action.show" class="col-xs-4 col-sm-3 col-md-3 explicit-page-min-height">
                            <q-btn no-caps flat :disable="checkDisable(item, action)" @click="action.fn(item)" class="fit">
                              <div>
                                <q-icon :name="action.icon"></q-icon>
                                <div class="q-pt-sm">{{ action.label }}</div>
                              </div>
                            </q-btn>
                            <q-tooltip v-if="action.tips && action.disable" max-width="300px">{{ action.tips }}</q-tooltip>
                          </div>
                        </template>
                      </template>
                    </div>
                  </q-expansion-item>
                </q-expansion-item>
              </template>
            </template>
            <template v-else>
              <template v-if="isLimit && !isVideoMode">
                <q-expansion-item class="bg-white shadow-2 overflow-hidden rounded-borders-md q-my-md" v-if="!attributes?.tool">
                  <template v-slot:header>
                    <q-item-section>
                      <div class="row">
                        <div class="col text-subtitle1 text-weight-medium">Link contents</div>
                        <div class="row items-center">
                          <span class="q-pl-sm text-grey">
                            {{ uniqueList.length }} {{ isContents ? 'Content' : 'Lesson' }}{{ uniqueList.length > 1 ? 's' : '' }}
                          </span>
                        </div>
                      </div>
                    </q-item-section>
                  </template>
                  <template v-for="(link, index) in uniqueList" :key="index">
                    <q-separator />
                    <SessionBoard
                      @click="onLinkItemClick"
                      :isLibrary="isLib"
                      :isMyContent="isContents"
                      isPreviewPage
                      :isLimit="isLimit"
                      :isSysView="isSysView"
                      :isVerify="isVerify"
                      :authId="route.query.authId"
                      no-border
                      :no-avatar="!!route.query.times || isSysView"
                      :no-price="isLib"
                      :courseId="attributes.course ? id : null"
                      :session="link"
                      :hideAuthor="true"></SessionBoard>
                  </template>
                  <NoData v-if="!uniqueList.length" size="9rem"></NoData>
                </q-expansion-item>
              </template>
              <template v-else>
                <q-expansion-item v-for="(group, index) in groups" :key="index" group="somegroup" class="overflow-hidden q-border-1 rounded-borders-md q-mb-md">
                  <template v-slot:header>
                    <q-item-section>
                      <div class="row">
                        <div class="col">{{ group.name }}</div>
                        <div class="row items-center">
                          <q-icon name="dynamic_feed" color="grey" size="1rem" />
                          <span class="q-pl-sm text-grey">
                            {{ getMainLinksByGroup(group).length }} {{ isContents ? 'Content' : 'Lesson'
                            }}{{ getMainLinksByGroup(group).length > 1 ? 's' : '' }}
                          </span>
                        </div>
                      </div>
                    </q-item-section>
                  </template>
                  <template v-for="(link, index) in getMainLinksByGroup(group)" :key="index">
                    <q-separator />
                    <SessionBoard
                      @click="onLinkItemClick"
                      :isLibrary="isLib"
                      :isMyContent="isContents"
                      isPreviewPage
                      :isLimit="isLimit"
                      no-border
                      :no-price="isLib"
                      :courseId="attributes.course ? id : null"
                      :session="link"></SessionBoard>
                  </template>
                  <NoData v-if="!getMainLinksByGroup(group).length" size="9rem"></NoData>
                </q-expansion-item>
              </template>
            </template>
          </q-list>
          <template v-if="!Acan.isEmpty(relevant)">
            <div class="full-width q-pa-md text-body1">Relevant content</div>
            <div class="q-px-md">
              <SessionBoard :session="relevant" isRelevant></SessionBoard>
            </div>
          </template>
          <div class="q-pa-md" v-if="showServicePackInfo && !route.query?.back?.includes('/detail/booked') && !route.query?.back?.includes('/service/pack')">
            <div class="text-center full-width q-pb-md text-weight-medium">Bundled Mentoring service package</div>
            <PackageCard
              no-price
              :pack="servicePack"
              :pack-user="servicePackUser"
              :is-creator="isCreator"
              :is-promotion="coursession.promotion"
              :is-educator="isCreator || isCoTeacher"></PackageCard>
          </div>
          <div class="q-pa-md" v-if="lastLecture?.sessionInfo && lastLecture?.sessionInfo?._id !== coursession?._id">
            <div class="full-width q-pb-md text-weight-medium">Referral content</div>
            <SessionBoard :session="lastLecture.sessionInfo" isRelevant></SessionBoard>
          </div>
          <div class="q-pa-xl q-ma-xl"></div>
        </q-page>
      </template>
      <!--
      <PublishDialog edit :show="showPublishDialog" :one="content" @hide="onPublishDialogHide"></PublishDialog>
      -->
      <InviteDialog :show="showInviteDialog" apply @apply="applyFn" @hide="onInviteDialogHide"></InviteDialog>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {ref, watch, onMounted, computed, inject} from 'vue'
import {useRoute, useRouter, onBeforeRouteUpdate} from 'vue-router'
import {date} from 'quasar'
import {pubStore} from 'stores/pub'
import {collabStore} from 'stores/collab'
import {slidesStore} from 'stores/slides'
import {curriculumStore} from 'stores/curriculum'
import OnePage from 'src/pages/account/assessment-tool/OnePage.vue'
import PersonAvatar from 'src/pages/account/assessment-tool/components/PersonAvatar.vue'
import SubjectChip from 'components/SubjectChip.vue'
import PackageCard from 'components/PackageCard.vue'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import OwnerBox from 'components/detail/OwnerBox.vue'
import PromptPage from 'components/PromptPage.vue'
import SessionBoard from 'components/SessionBoard.vue'
import SessionChips from 'components/SessionChips.vue'
import SessionChipsServiceAuth from 'components/SessionChipsServiceAuth.vue'
import PackageChips from 'components/PackageChips.vue'
import SessionStatus from 'components/SessionStatus.vue'
import SessionTime from 'components/SessionTime.vue'
import ShareBtn from 'components/ShareBtn.vue'
import SessionPrice from 'components/SessionPrice.vue'
import InviteDialog from 'components/InviteDialog.vue'
import nameFormatter from 'src/utils/formatters/nameFormatter.js'
import AddCartDialog from 'src/pages/order/AddCartDialog.vue'
import EvaluateDialog from 'components/EvaluateDialog.vue'
import useTeacherVerificationAuth from 'src/composables/account/teacher-verification/useTeacherVerificationAuth'
import useUnit from 'src/composables/account/unit/useUnit.js'
import {pointStore} from 'stores/point'
import InviteBtn from 'src/pages/substitute/InviteBtn.vue'
import {subRole} from 'src/pages/substitute/consts'
import IncomePrice from 'src/pages/substitute/IncomePrice.vue'

/*
props
*/
const props = defineProps({id: String, isContentSelect: Boolean, isImport: Boolean, isMyPublished: Boolean})

/*
consts
*/
const route = useRoute()
const router = useRouter()
const pStore = pointStore()
const isPointMode = ref(false)
const {list: authList, getList: getAuthList} = useTeacherVerificationAuth()
const {getOneById, removeOneById, patchOneById, copyOne, unPublish, relateLinkList, allRelateLinkList} = useUnit()
const id = ref(route.params.id)
const isCoursePage = ref(!!route.params.pid)
const course = ref({})
const relevant = ref({})
const servicePack = ref({})
const servicePackUser = ref({})
const serviceBooking = ref(null)
const isInterviewConsultant = ref(false)
const isCarerConsultant = ref(false)
const is1V1MentorAndContentOrientatedNotEnable = ref(false)
const isPremiumLecture = ref(false)
const contentsType = inject('ContentsType')
const serviceType = inject('ServiceType')
const stateMap = inject('StateMap')
const contentTab = ref(route.params.tab)
const loading = ref(true)
const updating = ref(false)
const ending = ref(false)
const expanded = ref(true)
const curriculum = curriculumStore()
const collab = collabStore()
const collabs = ref({})
const collabDocId = ref(null)
const collabMemberId = ref(null)
const activeCollabItem = ref({})
const gradeOptions = ref(null)
const pub = pubStore()
const slides = slidesStore()
const content = ref(null)
const source = ref(null)
const members = ref([])
const isTaskMode = ref(false)
const isVideoMode = ref(false)
const isAvlVideo = ref(false)
const isUnitMode = ref(false)
const isSelfStudy = ref(false)
const isTaskWorkshop = ref(false)
const isSysView = ref(!!route.query.isSysView)
const isVerify = ref(!!route.query.isVerify)

const auditor = ref({})
const serviceAuth = ref({})
const snapshot = ref({})
const isParentCoursePublic = ref(false)
const attributes = ref({})
const takeaway = ref({})
const studentData = ref({})
const myTakeaway = ref({})

const courseStartTimeInRange = ref(false)
const courseTimeState = ref(null)
const courseRegDeadlineExpired = ref(false)
//const isStudent = ref(false)
const isStudent = ref(pub.user?.roles?.includes('student'))
const isAdmin = ref(pub.user?.schoolUser?.role?.includes('admin'))
const isPersonal = ref(!pub.user?.schoolInfo?._id)
const isStudentCenter = ref(false)
const isStudentStudy = ref(false)

const isCreator = ref(false)
const isAuthor = ref(false)
const isAccident = ref(false)
const isInTheSameSchool = ref(false)
const isInTheSameClass = ref(false)
const isWriterRole = ref(false)
const isCollaborated = ref(false)
const rightDrawerOpen = ref(false)
const outline = ref({})
const relateTaskList = ref([])
const reviewList = ref([])
const ratingModel = ref(3.5)
const myRating = ref(0)
const sessionTab = ref('scheduled')
const activedLink = ref(null)
const showInviteDialog = ref(false)
const showReeditDialog = ref(false)
//const showPublishDialog = ref(false)
const reeditTarget = ref(null)
const isReopen = ref(false)
const subjects = ref([])
const coursession = ref(null)
const lastLecture = ref(null)
const booking = ref(null)
const isUnavailable = ref(false)
const groups = ref([])
const ageRange = ref({
  min: 3,
  max: 18,
})
const regData = ref(null)
const courseRegData = ref(null)
const isLogin = ref(!!pub.user?._id)
const regDeadlineExpired = ref(false)
const discountFinished = ref(false)
const roomStat = ref({})
const isCoTeacher = ref(false)
const isInRoster = ref(false)
const classesList = ref([])

const collecting = ref(false)
const duplicating = ref(false)
const reopening = ref(false)
const leaving = ref(false)
const buying = ref(false)
const copying = ref(false)
const evaluating = ref(false)
const enrolling = ref(false)

const topRating = ref(0)
const myReview = ref(null)
const myNote = ref(null)
const deadline = ref(null)
const linkList = ref([])
const uniqueList = ref([])
const isInCart = ref(false)
const dateFormat = inject('DATE_FORMAT_NZ')

const orderedService = ref([])

const ratingChartData = ref({
  labels: ['5 stars', '4 stars', '3 stars', '2 stars', '1 star'],
  datasets: [],
})

const ageChartData = ref({
  labels: [],
  datasets: [],
})

const editComment = ref(false)
const startTimeInRange = ref(false)
const loadingTime = ref(true)
const sessionTimeState = ref(null)
const unsatisfiedOptions = inject('UnsatisfiedOptions')
const evaluationRes = ref({})
const accidentRes = ref({})
const studentId = ref(route.params.sid)
const bookId = ref(route.query.bid)

/*
computed
*/

const isVerifyOrSysView = computed(() => {
  return isVerify.value || isSysView.value
})

const showNav = computed(() => {
  return !isVerifyOrSysView.value
})

const isInTheStudentsList = computed(() => {
  return coursession.value?.students?.some((e) => e == pub?.user?._id)
})

const studentsCount = computed(() => {
  return coursession.value?.count?.students || members.value?.length || 0
})

const verificationList = computed(() => {
  return authList.value.filter((e) => e.status === 2 && e.type == 'workshop')
})

const isLecturer = computed(() => {
  return isCreator.value || isSubTeacher.value || isCoTeacher.value
})

const isPremiumContent = computed(() => {
  if (verificationList.value?.length && isContents.value) {
    return verificationList.value.some(
      (e) =>
        (content.value.curriculum == 'pd' && e.curriculum == 'pd' && content.value.service?.type?.includes(e.subject)) ||
        (content.value.curriculum !== 'pd' && content.value.curriculum == e.curriculum)
    )
  }

  return false
})

const republished = computed(() => {
  if (content.value?.order?._id) {
    const link = content.value.order?.links?.find((item) => item.id == content.value?._id)
    const _parsed = Acan.ObjectIdParse(link?.newId)
    return new Date(content.value?.updatedAt) > new Date(_parsed.date)
  } else if (source.value) {
    return new Date(source.value.updatedAt) > new Date(content.value.createdAt)
  }
  return false
})

const showServicePackInfo = computed(() => {
  return (
    !loading.value &&
    coursession.value?.servicePack?._id &&
    (servicePackUser.value?._id || (servicePack.value?._id && (servicePack.value?.status || isEnrolled.value))) &&
    (!coursession.value?.promotion ||
      (coursession.value?.promotion &&
        (!pub.user?.freeServiceType?.[coursession.value.servicePack._id] ||
          (coursession.value?.regData?.order && pub.user?.freeServiceType?.[coursession.value.servicePack._id] == coursession.value?.regData?.order))))
  )
})

const isLectureRoom = computed(() => {
  return isStudent.value && ['taskSchoolWorkshop', 'unitSchoolCourses', 'pdSchoolStudentWorkshop', 'pdSchoolStudentCourses'].includes(coursession.value.type)
})

const isScoreEnabled = computed(() => {
  //https://github.com/zran-nz/bug/issues/5615#issuecomment-2559080058
  if (coursession.value?.questions && Array.isArray(coursession.value.questions)) {
    for (const question of coursession.value?.questions) {
      for (const key in question.score) {
        if (question.score[key].enable === true) {
          return true
        }
      }
    }
  }
  return false
})

const theServiceType = computed(() => {
  return serviceType?.[attributes.value.type]
})

const isMyClass = computed(() => {
  return !attributes.value.content && (attributes.value.class || attributes.value.colleague)
  //return ['courses', 'session'].includes(coursession.value?.type)
})

const disableTipsForAT = computed(() => {
  const _attr = getAttributes(course.value)
  return (isCoursePage.value && _attr.course && !course.value.students?.length) ||
    (!isCoursePage.value && isMyClass.value && !coursession.value.students?.length)
    ? 'There are no students chosen under this session which you have scheduled. '
    : `Please wait for your ${isCreator.value ? 'students' : 'classmates'} to enroll patiently.`
})

const disableEnterClassForAT = computed(() => {
  if (!isCoursePage.value && coursession.value?.booking) return false
  const _attr = getAttributes(course.value)
  if (isCoursePage.value) {
    if (isATDisabled.value) return true
    if (_attr.enroll && !course.value.regNum) return true
    if (!_attr.enroll && !course.value.students?.length) return true
    return false
  }

  if (!isCoursePage.value) {
    if (isMyClass.value && !coursession.value.students?.length) return true
    if (!isMyClass.value && isATDisabled.value) return true
    return false
  }

  return true
  // return (
  //   (isCoursePage.value && (isATDisabled.value || (_attr.enroll && !course.value.regNum) || (!_attr.enroll && !course.value.students?.length))) ||
  //   (!isCoursePage.value && ((isMyClass.value && !coursession.value.students?.length) || (!isMyClass.value && isATDisabled.value)))
  // )
})

const coursessionStatus = computed(() => {
  if (coursession.value) {
    return getItemStatus(coursession.value)
  } else {
    return false
  }
})

const contentsCount = computed(() => {
  let sum = isUnitMode.value ? 0 : 1
  if (linkList.value.length) {
    sum += linkList.value.length
  }

  if (sum) {
    return `${sum} content${sum > 1 ? 's' : ''}`
  } else {
    return false
  }
})

const sessionsCount = computed(() => {
  if (attributes.value.session) {
    if (coursession.value?.sessionType == 'live') {
      return '1 live session'
    } else {
      return '1 self-study session'
    }
  } else if (attributes.value.course) {
    const total = linkList.value.length ?? 0
    const liveSessions = linkList.value.filter((item) => item.sessionType == 'live')
    const count = liveSessions?.length ?? 0

    if (count) {
      return `${count} live session${count > 1 ? 's' : ''}`
    } else if (total - count) {
      return `${total - count} self-study session${total - count > 1 ? 's' : ''}`
    } else {
      return null
    }
  } else {
    return null
  }
})

const isContents = computed(() => {
  return route.path.includes('/content/') || props.isContentSelect || props.isImport
})

const isContentView = computed(() => {
  return isContents.value || isLib.value
})

const isLib = computed(() => {
  //return route.params.type == 'lib' && !props.id
  return route.path.includes('/lib/') && !props.id
})

const isLimit = computed(() => {
  return (isLib.value && route.path.includes('/limit/')) || contentTab.value == 'limit'
})

const isNotInAnIframe = computed(() => {
  return window.self === window.parent
})

const canShareOrBookmark = computed(() => {
  const obj = {share: false, bookmark: false}

  if (isLib.value || isSelfStudy.value || attributes.value.public) {
    obj.share = true
  }

  if (pub.user?.school) {
    const backParams = new URLSearchParams(route.query?.back?.split('?')?.[1])
    const tab = backParams.get('tab')
    if (isStudent.value) {
      if (route.query?.back?.includes('/study/class') && tab === 'lecture') {
        obj.share = false
      }
    } else {
      if (route.query?.back?.includes('/home/<USER>') && tab === 'workshop') {
        obj.share = false
      }
      if (route.query?.back?.includes('/home/<USER>') && tab === 'workshop') {
        obj.share = false
      }
    }
  }

  //if (isLib.value || isSelfStudy.value) {
  if (isLib.value) {
    obj.bookmark = true
  }

  return obj
})

const isEnrolled = computed(() => regData.value?._id == pub.user._id || courseRegData.value?._id == pub.user._id)

const isCancellable = computed(() => {
  if (isCoursePage.value) {
    return !courseStartTimeInRange.value && courseTimeState.value === 'start'
  } else {
    return !startTimeInRange.value && coursessionStatus.value === 'Scheduled'
  }
})

const isOperateDisabled = computed(() => {
  return (
    !isMyClass.value &&
    ((!isCoursePage.value && startTimeInRange.value) || (isCoursePage.value && (courseStartTimeInRange.value || courseTimeState.value !== 'start')))
  )
})

const hasPeerATMap = ref({})
async function checkATHasPeer(item) {
  const cid = item?.cid
  if (!Object.keys(hasPeerATMap.value).includes(cid)) {
    const res = await getOneById(cid)
    const toolGroup = res?.toolGroup || []
    const hasPeer = toolGroup.some((e) => e?.peer)
    hasPeerATMap.value[cid] = hasPeer
  }
  return hasPeerATMap.value[cid]
}
function checkDisable(item, action) {
  if (item?.mode !== 'tool') return action.disable
  if (!hasPeerATMap.value?.[item?.cid]) return false
  return action.disable
}

const isATDisabled = computed(() => {
  const isATHasPeerReview = coursession.value?.task?.toolData
  if (!isATHasPeerReview) return false

  if (isCoursePage.value) {
    return !courseRegDeadlineExpired.value || (!courseStartTimeInRange.value && courseTimeState.value === 'start')
  } else {
    return !regDeadlineExpired.value || (!startTimeInRange.value && coursessionStatus.value === 'Scheduled')
  }
})

const listItems = computed(() => {
  console.log('showQuestion', content.value)
  const o = content.value
  const servicePackBundled = !!studentId.value //https://github.com/zran-nz/bug/issues/5122
  const isScheduled = !attributes.value.content && coursessionStatus.value == 'Scheduled'
  const isOngoing = !attributes.value.content && coursessionStatus.value == 'Ongoing'
  const isEnded = !attributes.value.content && coursessionStatus.value == 'Ended'
  const isTool = attributes.value.tool
  const isForEducator = attributes.value.educator
  const isBuy = !!o.order?._id
  let isCopy = false
  if (isBuy && o.order?.links?.length) {
    const link = o.order.links.find((item) => item.id == o._id && item.archived)
    isCopy = isBuy && link
  }

  let showOverview = false

  const isArchivedContent = isContents.value && o.del
  const isAuthorized = isCreator.value || isCollaborated.value

  const showBuyOrCopy = !isCreator.value && (isCopy || (isLib.value && !isBuy)) && !isLimit.value
  const showAddToCart =
    ((!isCreator.value && isLib.value && !isBuy) || (!isCreator.value && isSelfStudy.value && !isEnrolled.value)) && !isPointMode.value && !isLimit.value
  //https://github.com/zran-nz/bug/issues/5232
  const showCollaborate =
    isContents.value && !isAuthorized && !isTool && !isLimit.value && content.value?.sid && !content.value.sid.includes('hash:') && showNav.value
  let showSlides = isTaskMode.value && !(!isCreator.value && isScoreEnabled.value)

  let showEvaluation =
    isEnded &&
    attributes.value.type == 'booking' &&
    (!isCreator.value || ((isCreator.value || isSubTeacher.value) && coursession.value.rating)) &&
    !isLimit.value

  const showEvaluationForSubstitute = isEnded && isSubAdminMatched.value
  const disableEvaluation = showEvaluation && !coursession.value?.rating && new Date() - new Date(coursession.value.end) > 24 * 3600 * 1000
  const showEvaluationSubTeacher = isEnded && isSubTeacher.value
  const disableEvaluationSubTeacher = !coursession.value?.rating

  // const showUpdateSlides = showSlides && isAuthorized && isScheduled && isAuthor.value && !isCoursePage.value && !isLimit.value
  let showUpdateSlides = showSlides && isAuthorized && isScheduled && isAuthor.value && !isCoursePage.value && !isLimit.value

  //reflect: https://github.com/zran-nz/bug/issues/4948#issuecomment-2183908998
  const showReflection =
    (((isContents.value && isAuthorized) ||
      ((attributes.value.session || attributes.value.course) && ((isForEducator && (isCreator.value || isCoTeacher.value)) || !isForEducator))) &&
      !isTool &&
      !isStudent.value &&
      !isInterviewConsultant.value &&
      !isSubOrder.value &&
      !isLimit.value) ||
    ((isVerify.value || isSysView.value) && !isTool)
  const showTeachingNotes =
    ((isContents.value && o?.source) || isLib.value) && !isStudent.value && !((isVerify.value || isSysView.value) && isTool) && !serviceAuth.value?._id
  //https://github.com/zran-nz/bug/issues/5267#issue-2474668495
  const showEdit =
    (isBuy && !isCopy) ||
    (isAuthorized && attributes.value.content && !['arch', 'other'].includes(contentTab.value) && !isLimit.value && !(isLib.value && !isCreator.value))
  const showEditPrompts = showEdit && showSlides
  const showReschedule =
    !attributes.value.content &&
    isCreator.value &&
    isScheduled &&
    !coursession.value.regNum &&
    !isCoursePage.value &&
    !isLimit.value &&
    !coursession.value.booking
  const showSchedule = isAuthorized && isContents.value && !['arch', 'preview'].includes(contentTab.value) && !attributes.value.tool && !isLimit.value
  const showLeave = isContents.value && contentTab.value == 'other' && isAuthorized && !isLimit.value
  const showArchive = isCreator.value && isContents.value && ['me', 'share'].includes(contentTab.value) && !isLimit.value
  const showPublish =
    isCreator.value &&
    isContents.value &&
    ['me', 'share'].includes(contentTab.value) &&
    (!o.sourceUid || o.sourceUid === pub.user._id) &&
    !isTool &&
    isPersonal.value &&
    !isLimit.value
  //const showUnpublish = isCreator.value && (o.publish?.lib || coursession.value?.task?.publish?.link)
  const showUnpublish =
    isCreator.value && (o.publish?.lib || isSelfStudy.value) && !attributes.value.booking && !attributes.value.session && isPersonal.value && !isLimit.value
  const showBookmark = canShareOrBookmark.value.bookmark && !((isEnrolled.value || isBuy) && !o.collected) && !isLimit.value && !isCreator.value
  //const showDuplicate = (isCreator.value || isWriterRole.value) && isContents.value && contentTab.value == 'me' && !isTool
  const showDuplicate = (isCreator.value || isWriterRole.value) && isContents.value && !isTool && !isLimit.value
  const showLearningdata =
    !isAvlVideo.value && isCreator.value && !attributes.value.content && !isSelfStudy.value && !isLimit.value && !isInterviewConsultant.value
  const importSubCondition = isSubTeacher.value
  const showImport =
    isLecturer.value &&
    !attributes.value.content &&
    !attributes.value.tool &&
    !(attributes.value.session && coursession.value.sessionType == 'student') &&
    !isEnded &&
    !isLimit.value &&
    !isInterviewConsultant.value &&
    !isCarerConsultant.value
  const showReopen = isCreator.value && isEnded && (attributes.value.session || attributes.value.tool) && !isLimit.value && !coursession.value.booking
  const showEnd = isCreator.value && isOngoing && !isSelfStudy.value && !isLimit.value && !coursession.value.booking
  const showProject =
    (isCreator.value || isCoTeacher.value || isSubAdmin.value || isSubTeacher.value) &&
    attributes.value.session &&
    !isSelfStudy.value &&
    !isLimit.value &&
    !isInRoster.value
  const showDashboard = showProject
  const disableProjectAndDashboardAndReopen = attributes.value.session && attributes.value.booking && isEnded
  const showRoster =
    (isCreator.value || isSubAdmin.value || isSubTeacher.value) &&
    attributes.value.session &&
    !isSelfStudy.value &&
    !isLimit.value &&
    !isInterviewConsultant.value
  //const showStudyData = studentId.value && studentData.value?._id && isEnded && !isLimit.value
  const showStudyData = studentId.value && studentData.value?._id && !isLimit.value
  // const showTakeaway =
  //   (isStudent.value ||
  //     ((isCreator.value || isCoTeacher.value || isSubTeacher.value) &&
  //       !isTool &&
  //       !isContentView.value &&
  //       !isSelfStudy.value &&
  //       isEnded &&
  //       (coursession.value?.students?.length || coursession.value.regNum))) &&
  //   !isLimit.value
  let showTakeaway =
    ((isStudent.value && !isScheduled) ||
      ((isCreator.value || isCoTeacher.value || isSubTeacher.value) &&
        !isTool &&
        !isContentView.value &&
        !isSelfStudy.value &&
        isEnded &&
        (coursession.value?.students?.length || coursession.value.regNum))) &&
    !isLimit.value

  //const showAssessing = (isEnrolled.value || isCreator.value) && isTool && !isContentView.value && !isSelfStudy.value
  const showAssessing = isTool && !isContentView.value && !isSelfStudy.value && !isLimit.value
  const showCorrecting = isCreator.value && isTool && !isContentView.value && !isLimit.value
  const showRemove = isCreator.value && (isArchivedContent || isScheduled) && !isLimit.value // 老师cancel
  const showEnroll =
    attributes.value.enroll &&
    !isCreator.value &&
    !isCoTeacher.value &&
    (!isEnrolled.value || !pub.user?._id) &&
    (!isOngoing || isSelfStudy.value) &&
    !isCoursePage.value &&
    !isContentView.value &&
    !isLimit.value
  const disableEnroll = !!(coursession.value && coursession.value.regNum && coursession.value.regMax && coursession.value.regNum == coursession.value.regMax)
  const enrollDisabledTips = regDeadlineExpired.value ? 'Enrollment expired' : 'Maximum applicants reached'

  let showCancel =
    !isCreator.value &&
    isLogin.value &&
    (isEnrolled.value || attributes.value.booking || attributes.value.seeker) &&
    isScheduled &&
    !startTimeInRange.value &&
    sessionTimeState.value &&
    !isCoursePage.value &&
    !isLimit.value
  const showEnterClassForAT =
    attributes.value.tool && (!attributes.value.enroll || ((isCreator.value || isEnrolled.value) && !isCancellable.value)) && !isLimit.value
  //const showEnterClassForAT = false
  let showEnterClass =
    (isVideoMode.value && attributes.value.session && isStudent.value) ||
    (attributes.value.session &&
      ((!attributes.value.enroll && !isCreator.value && attributes.value.educator) ||
        (isStudent.value && (attributes.value.type == 'teaching' || attributes.value.booking || (isEnrolled.value && attributes.value.self))) ||
        ((sessionTimeState.value || courseTimeState.value) && isEnrolled.value && !isCancellable.value)) &&
      !isLimit.value)

  if (isStudent.value) {
    try {
      const backParams = new URLSearchParams(route.query?.back?.split('?')?.[1])
      const tab = backParams.get('tab')
      const subtab = backParams.get('subtab')

      // my purchased - workshop - scheduled : showEnterClass = true
      if (tab === 'workshop' && subtab === 'scheduled' && route.query?.back?.includes('/study/purchased')) {
        showEnterClass = true
      }

      // my purchased - mentoring - scheduled:showTakeaway = false, showOverview = true, showSlides = true

      if (tab === 'mentoring' && subtab === 'scheduled' && route.query?.back?.includes('/study/purchased')) {
        showTakeaway = false
        showOverview = true
        showSlides = true
        showCancel = true
      }

      //  my classes - private - ended : showSlides = true
      if (tab === 'private' && subtab === 'ended' && route.query?.back?.includes('/study/class')) {
        showSlides = true
      }

      // my purchased - mentoring - ended : showSlides = true showEvaluation = false
      if (tab === 'mentoring' && subtab === 'ended' && route.query?.back?.includes('/study/purchased')) {
        showOverview = true
        showSlides = true
        showEvaluation = false
      }

      // my classes - private - scheduled:showSlides = true

      if (tab === 'private' && subtab === 'scheduled' && route.query?.back?.includes('/study/class')) {
        showSlides = true
      }
    } catch (e) {
      console.log('StudentError', e)
    }
  } else {
    try {
      const backParams = new URLSearchParams(route.query?.back?.split('?')?.[1])
      const tab = backParams.get('tab')
      const subtab = backParams.get('subtab')

      // I facilitate - mentoring - scheduled : showUpdateSlides = false
      if (tab === 'mentoring' && subtab === 'scheduled' && route.query?.back?.includes('/home/<USER>')) {
        showUpdateSlides = false
      }
    } catch (e) {
      console.log('TeacherError', e)
    }
  }

  const showComplaint = false
  const showQuestion = isAvlVideo.value
  return [
    {
      button: false,
      icon: 'o_remove_red_eye',
      kidIcon: 'overview',
      label: 'Overview',
      fn: overviewFn,
      overview: true,
      show: !isInterviewConsultant.value || showOverview,
      disable: false,
    },
    {button: false, icon: 'o_slideshow', kidIcon: 'slides', label: 'Slides', fn: slidesFn, slides: true, show: showSlides && !isAvlVideo.value, disable: false},
    {button: false, icon: 'o_slideshow', kidIcon: 'slides', label: 'Questions', fn: questionFn, question: true, show: showQuestion, disable: false},
    {
      button: false,
      icon: !isCreator.value ? 'o_approval' : isAccident.value && !accidentRes.value?.evidencesTeacher?.length ? 'o_error_outline' : 'o_rate_review',
      label: !isCreator.value ? 'Review' : isAccident.value && !accidentRes.value?.evidencesTeacher?.length ? 'Appeal' : "Students's review",
      fn: evaluationFn,
      loading: evaluating.value,
      show: showEvaluation,
      disable: disableEvaluation,
      tips: "It's been 24 hours since the course ended, you can't leave a review anymore.",
      evaluation: true,
    },
    {
      button: false,
      icon: 'o_rate_review',
      label: "Students's review",
      fn: evaluationSubTeacherFn,
      loading: evaluating.value,
      show: showEvaluationSubTeacher,
      disable: disableEvaluationSubTeacher,
      tips: "It's been 24 hours since the course ended, you can't leave a review anymore.",
      evaluation: true,
    },
    {
      button: false,
      icon: 'o_approval',
      label: 'Review',
      fn: evaluationForSubstituteFn,
      loading: evaluating.value,
      show: showEvaluationForSubstitute,
      disable: disableEvaluation,
      tips: "It's been 24 hours since the course ended, you can't leave a review anymore.",
      evaluation: true,
    },
    {button: false, icon: 'o_change_circle', label: 'Update slides', fn: upslidesFn, show: showUpdateSlides, disable: false},
    {
      button: false,
      icon: 'o_devices',
      label: 'Projector view',
      fn: projectFn,
      show: !isVideoMode.value && showProject,
      disable: disableProjectAndDashboardAndReopen,
    },
    {button: false, icon: 'o_space_dashboard', label: 'Dashboard view', fn: dashboardFn, show: showDashboard, disable: disableProjectAndDashboardAndReopen},
    {button: false, icon: 'o_diversity_1', label: 'Apply to collaborate', fn: collaborateFn, show: showCollaborate, disable: false},
    {button: false, icon: 'o_event_note', label: 'Schedule', fn: scheduleFn, show: showSchedule},
    {button: false, icon: 'o_subscriptions', label: 'Create sub-task', show: false, disable: false},
    {
      button: false,
      icon: 'o_drive_folder_upload',
      label: o?.publish?.lib ? 'Republish' : 'Publish',
      fn: publishFn,
      show: showPublish,
      disable: false,
    },
    {button: false, icon: 'o_article', label: 'Learning data', fn: learningDataFn, show: showLearningdata, disable: false},
    {
      button: false,
      icon: 'o_format_list_bulleted',
      label: 'Class roster',
      fn: rosterFn,
      show: showRoster,
      roster: true,
      disable: disableProjectAndDashboardAndReopen,
    },
    {button: false, icon: 'o_grading', label: 'Correcting service', fn: correctingFn, show: showCorrecting, correct: true, disable: false},
    {
      button: false,
      icon: 'o_ballot',
      kidIcon: 'takeaway',
      label: takeaway.value?._id || isStudent.value || attributes.value.booking ? 'Takeaway' : 'Generate takeaway',
      fn: takeawayFn,
      show: showTakeaway,
      takeaway: true,
      disable: false,
    },
    {
      button: false,
      icon: 'o_ballot',
      kidIcon: 'takeaway',
      label: 'Study data',
      fn: studentDataFn,
      show: showStudyData,
      //show: true,
      studyData: true,
      takeaway: true,
      disable: false,
    },
    //{button: false, icon: 'o_add_link', kidIcon: 'message', label: 'Message', fn: takeawayFn, show: true, disable: false},
    {
      button: false,
      icon: 'o_pageview',
      label: isVerify.value && route.query.back?.includes('sys') ? 'Evaluation' : 'Reflection',
      fn: reflectFn,
      show: showReflection,
      disable: false,
    },
    {button: false, icon: 'o_add_task', label: 'Import', fn: importFn, show: showImport, disable: false},
    {
      button: false,
      icon: 'o_note_alt',
      label: 'Teaching notes',
      caption: 'Teaching experience shared by others',
      fn: notesFn,
      show: showTeachingNotes,
      disable: false,
    },
    {button: false, icon: 'o_thumb_down_off_alt', label: 'Complaint', fn: complaintFn, show: showComplaint, disable: false},
    {
      button: false,
      icon: 'o_edit',
      label: 'Reschedule',
      fn: rescheduleFn,
      show: showReschedule,
    },
    {
      button: false,
      icon: 'o_edit',
      label: 'Edit',
      fn: editFn,
      show: showEdit,
      disable: attributes.value.enroll && isOperateDisabled.value,
      tips: 'Since the enrolled users can no longer cancel the enrollment so you are not permitted to edit/delete the course/session to avoid disputes caused by changes.',
    },
    {
      button: false,
      icon: 'o_post_add',
      label: 'Edit prompts',
      fn: promptsFn,
      show: showEditPrompts,
      disable: !(content.value?.sid && !content.value.sid.includes('hash:')),
      tips: 'Please click the “Edit” button above, then choose "Edit Slides", click the “Initiate slides” button to save the copied slides into your drive before editing prompts.',
      //show: false,
    },
    {
      button: false,
      icon: 'o_format_list_bulleted',
      label: 'Assessing status',
      fn: assessingFn,
      show: showAssessing,
      assess: true,
      disable: false,
      self: isSelfStudy.value,
      all: isCreator.value || isEnrolled.value || !attributes.value.enroll,
    },
    {button: true, icon: 'o_inventory_2', label: 'Archive', fn: archiveFn, show: showArchive, disable: o?.publish?.lib},
    {button: true, icon: 'o_restore', label: 'Restore', fn: restoreFn, show: isArchivedContent, disable: false},
    {
      button: true,
      icon: 'o_remove',
      label: isArchivedContent ? 'Delete' : 'Cancel',
      fn: removeFn,
      show: showRemove,
      dialog: true,
      disable:
        isOperateDisabled.value && (isPremiumLecture.value || (isPersonal.value && (attributes.value.enroll || (isCoursePage.value && attributes.value.tool)))),
      tips: isPremiumLecture.value
        ? 'You are about to cancel this session within 2 hours prior to the starting time, which results in any cost involved being deducted.'
        : 'You can no longer cancel the session within 12 hours prior to the start time',
    },
    {button: true, icon: 'o_cancel', label: 'Leave', fn: leaveFn, loading: leaving.value, show: showLeave, disable: false},
    {
      button: true,
      icon: 'o_restart_alt',
      label: 'Reopen',
      fn: reopenFn,
      loading: reopening.value,
      show: showReopen && !coursession.value.type?.toLowerCase()?.includes('workshop'),
      disable: disableProjectAndDashboardAndReopen,
    },
    {button: true, icon: 'o_stop_circle', label: 'End', fn: endFn, loading: ending.value, show: showEnd, disable: false},
    {button: true, icon: 'o_local_library', label: 'Enter class', fn: enterclassFn, show: showEnterClass, disable: false},
    {button: true, icon: 'o_cancel', label: 'Cancel', fn: cancelFn, show: showCancel, disable: false},
    {button: true, unelevated: true, label: 'Terms and policy', fn: policyClick, show: showCancel, disable: false},
    {
      button: true,
      icon: 'o_local_library',
      label: isLogin.value ? 'Enroll now' : 'Login',
      fn: enrollFn,
      loading: enrolling.value,
      show: showEnroll,
      disable: disableEnroll,
      tips: disableEnroll ? enrollDisabledTips : '',
    },
    {
      button: true,
      icon: 'o_inventory_2',
      label: isCopy ? 'Copy' : 'Buy now',
      fn: isCopy ? copyFn : buyFn,
      loading: buying.value || copying.value,
      show: showBuyOrCopy,
    },
    {
      button: true,
      icon: 'o_shopping_cart',
      unelevated: true,
      label: isInCart.value ? 'In the cart' : 'Add to cart',
      fn: cartFn,
      show: showAddToCart,
    },
    {
      button: true,
      icon: 'o_cancel',
      label: 'Unpublish',
      fn: unpublishFn,
      show: showUnpublish,
      disable: false,
    },
    {
      button: true,
      unelevated: true,
      icon: o.collected ? 'o_bookmark_remove' : 'o_bookmark_border',
      label: o.collected ? 'Remove bookmark' : 'Bookmark',
      fn: collectFn,
      loading: collecting.value,
      show: showBookmark,
    },
    {button: true, icon: 'o_file_copy', label: 'Duplicate', fn: duplicateFn, loading: duplicating.value, show: showDuplicate, disable: false},
    {
      button: true,
      icon: 'o_local_library',
      label: 'Enter class',
      fn: enterclassFn,
      show: showEnterClassForAT,
      disable: disableEnterClassForAT.value,
      tips: disableTipsForAT.value,
    },
  ]
})

const isSubOrder = computed(() => {
  return route.query.subform === 'subTeacher' && coursession.value.substituteTeacherStatus == 0
})

const isSubTeacher = computed(() => {
  return pub.user?._id === coursession.value?.substituteTeacher && route.query.subform === 'subTeacher' && coursession.value.substituteTeacherStatus == 1
})

const isSubAdmin = computed(() => {
  return subRole(coursession.value, route.query.subform, pub.user._id) === 'admin'
})

const isSubAdminMatched = computed(() => {
  return subRole(coursession.value, route.query.subform, pub.user._id) === 'admin' && coursession.value.substituteTeacherStatus == 1
})

const isSubClassMatched = computed(() => {
  return subRole(coursession.value, route.query.subform, pub.user._id) === 'class' && coursession.value.substituteTeacherStatus == 1
})

/*
methods
*/

const updateToLatestVersion = async () => {
  $q.dialog({
    message: 'By clicking this button, all content will be overwritten and updated. This action is irreversible. Do you confirm the update?',
    ok: {
      label: 'Yes update',
      rounded: true,
      noCaps: true,
    },
    cancel: {
      label: 'Not this time',
      outline: true,
      rounded: true,
      noCaps: true,
    },
    persistent: true,
  }).onOk(async () => {
    updating.value = true

    const link = content.value.order?.links?.find((item) => item.id == content.value._id)
    await removeOneById(source.value ? content.value._id : link.newId)
    const _id = source.value ? content.value.source : content.value._id
    const orderId = source.value ? source.value.order._id : content.value.order._id

    const rs = await copyOne({_id, orderId})
    updating.value = false
    if (!rs) {
      $q.notify({type: 'negative', message: 'Updated unsuccessfully'})
    } else {
      router.replace({path: `/com/${attributes.value.unit ? 'unit' : 'task'}/edit/${rs._id}`, query: {back: '/home/<USER>'}})
      $q.notify({type: 'positive', message: 'Updated successfully'})
    }
    updating.value = false
  })
}

const onLinkItemClick = (path) => {
  console.log(path, '<============onLinkItemClick')
}

const onCourseStartTimeChanged = (val) => {
  courseStartTimeInRange.value = val
}

const onCourseTimeLoaded = (val) => {
  courseTimeState.value = val
}

const onCourseRegDeadlineExpired = () => {
  courseRegDeadlineExpired.value = true
}

const onDiscountFinished = () => {
  discountFinished.value = true
}

const onRegDeadlineLoaded = () => {}
const onRegDeadlineExpired = () => {
  regDeadlineExpired.value = true
}

const getEnrolledMembers = async () => {
  const rs = await App.service('session').get('reg', {query: {_id: coursession.value._id}})
  members.value = rs?.reg ?? []
  isInRoster.value = members.value?.some((e) => e._id == pub.user._id)
}

/*
const onPublishDialogHide = (changed) => {
  showPublishDialog.value = false
  if (changed) {
    window.location.reload()
  }
}*/

const onInviteDialogHide = () => {
  showInviteDialog.value = false
}

const onReeditDialogHide = (changed) => {
  showReeditDialog.value = false
  isReopen.value = false
  reeditTarget.value = null
  if (changed) {
    window.location.reload()
  }
}

const applyFn = async (message) => {
  if (collabDocId.value) {
    let applyId = collabDocId.value
    if (activeCollabItem.value._id !== content.value._id) {
      const _collab = collabs.value?.[activeCollabItem.value.id]
      if (_collab) {
        applyId = _collab.collab
      } else {
        const {_id} = await collab.get(activeCollabItem.value._id, activeCollabItem.value.mode)
        applyId = _id
      }
    }
    await collab.apply(applyId, message)
    $q.notify({type: 'info', message: 'Successful'})
  }
}

const avatarDisabled = (type) => {
  let _total = attributes.value.session && attributes.value.public ? coursession.value.regNum : coursession.value.students?.length
  if (isCoursePage.value) {
    _total = isParentCoursePublic.value ? course.value.regNum : course.value.students?.length
  }
  _total = _total ?? 0

  if (type == 'self') {
    if (isCreator.value) {
      return _total == 0
    } else {
      return false
    }
  } else if (type == 'others') {
    return _total < 2
  } else if (type == 'teacher') {
    return _total < 1
  }
}

const getTextAndChecked = (item, type) => {
  const _stat = isCreator.value ? item.toolStat?.[type] : item.toolStat?.[pub.user._id]?.[type]
  let _total = attributes.value.session && attributes.value.public ? coursession.value.regNum : coursession.value.students?.length
  if (isCoursePage.value) {
    _total = isParentCoursePublic.value ? course.value.regNum : course.value.students?.length
  }

  const obj = {text: null, checked: false}

  if (isCreator.value) {
    if (_stat == _total) {
      obj.checked = true
    } else {
      obj.text = `${_stat ?? 0}/${_total}`
    }
  } else {
    if (type == 'self' && _stat) {
      obj.checked = true
    } else if (type == 'others') {
      const __total = _total >= 3 ? 2 : _total - 1
      if (_stat == __total) {
        obj.checked = true
      } else if (__total > 0) {
        obj.text = `${_stat ?? 0}/${__total}`
      }
    }
  }

  if (avatarDisabled(type)) {
    return {text: null, checked: false}
  } else {
    return obj
  }
}

const getActionsByItem = (item) => {
  console.log(item, 'item')
  const isTask = ['task', 'pdTask'].includes(item.mode)
  const sessionAttrbutes = contentsType[item.type]
  const isToolSession = !sessionAttrbutes?.content && sessionAttrbutes?.tool
  const itemStatus = getItemStatus(item)
  const isScheduled = itemStatus == 'Scheduled'
  const isOngoing = itemStatus == 'Ongoing'
  const isEnded = itemStatus == 'Ended'
  const subOngoing = isOngoing && isSubClassMatched.value
  const showUpdateSlides = isTask && isCreator.value && pub.user._id === item.task?.uid && !subOngoing

  const showEdit = (isCreator.value || isSubTeacher.value || collabs.value?.[item.id]?.status) && isScheduled
  const showDelete = showEdit && !isCarerConsultant.value && !isInterviewConsultant.value
  const showEnd =
    (isCreator.value || isCoTeacher.value || collabs.value?.[item.id]?.status || isSubTeacher.value) &&
    isOngoing &&
    !isCarerConsultant.value &&
    !isInterviewConsultant.value
  const showReopen =
    (isCreator.value || isCoTeacher.value || collabs.value?.[item.id]?.status || isSubTeacher.value) &&
    isEnded &&
    !isCarerConsultant.value &&
    !isInterviewConsultant.value

  // SubTeacher after class is not show reflection
  const subReflectionPre = (isOngoing || isEnded) && isSubTeacher.value && item?.groupName !== 'After class'
  const showReflection =
    !serviceAuth.value?._id && ((!isToolSession && (isCreator.value || isCoTeacher.value)) || collabs.value?.[item.id]?.status || subReflectionPre)
  const showCollaborate = isContents.value && !isCreator.value && !collabs.value?.[item.id]?.email && item.sid && !item.sid.includes('hash:') && showNav.value
  const showCollaborateWaiting = isContents.value && !showEdit && !showCollaborate && !isCreator.value && showNav.value
  let showEnterClassForAT = isToolSession && (!attributes.value?.enroll || isCreator.value || isCoTeacher.value || (isEnrolled.value && !isCancellable.value))

  const enterClassSubCondition = isSubAdmin.value || isSubTeacher.value
  let showEnterClass =
    (isVideoMode.value ||
      (!isContentView.value && !isToolSession && (!attributes.value?.enroll || (isEnrolled.value && !isCancellable.value)) && !isCreator.value)) &&
    !enterClassSubCondition
  const showProject =
    !isToolSession && (isCreator.value || isCoTeacher.value || isSubAdmin.value || isSubTeacher.value) && !isContentView.value && !isInRoster.value
  const showDashboard = showProject
  const showTakeaway = !isToolSession && isCreator.value && !isContentView.value && isEnded && studentsCount.value
  const showToolAvatar = isToolSession && (!attributes.value?.enroll || ((isSelfStudy.value || isCreator.value || isEnrolled.value) && !isCancellable.value))
  const showStudyData = !isToolSession && studentId.value && !isLimit.value

  if (isStudent.value) {
  } else {
    const backParams = new URLSearchParams(route.query?.back?.split('?')?.[1])
    const by = backParams.get('by')
    if (pub.user?.school && route.query?.back?.includes('/home/<USER>') && by === 'others') {
      showEnterClass = false
      showEnterClassForAT = false
    }
  }
  if (item?.mode === 'tool') checkATHasPeer(item)

  return [
    {
      icon: 'o_local_library',
      kidIcon: '',
      label: 'Enter',
      fn: enterclassFn,
      push: true,
      show: showEnterClassForAT,
      disable: disableEnterClassForAT.value,
      tips: disableTipsForAT.value,
    },
    {icon: 'o_local_library', kidIcon: '', label: 'Enter class', push: true, fn: enterclassFn, show: showEnterClass, disable: false},
    {icon: 'o_remove_red_eye', kidIcon: 'overview', label: 'Overview', fn: overviewFn, overview: true, show: true, disable: false},
    {icon: 'o_devices', label: 'Project', fn: projectFn, show: showProject, disable: false},
    {icon: 'o_space_dashboard', label: 'Dashboard', fn: dashboardFn, show: showDashboard, disable: false},
    {icon: 'o_ballot', kidIcon: 'takeaway', label: 'Takeaway', fn: takeawayFn, show: showTakeaway, disable: false},
    {icon: 'o_slideshow', kidIcon: 'slides', label: 'Slides', fn: slidesFn, slides: true, show: isTask, disable: false},
    {icon: 'o_published_with_changes', label: 'Update slides', fn: upslidesFn, slides: true, show: showUpdateSlides, disable: false},
    {icon: 'o_slideshow', kidIcon: 'slides', label: 'Questions', fn: questionFn, question: true, show: isAvlVideo.value, disable: false},
    {icon: 'o_pageview', label: isVerify.value ? 'Evaluation' : 'Reflection', fn: reflectFn, show: showReflection, disable: false},
    {
      button: false,
      icon: 'o_ballot',
      kidIcon: 'takeaway',
      label: 'Study data',
      fn: studentDataFn,
      show: showStudyData,
      studyData: true,
      takeaway: true,
      disable: false,
    },
    {
      icon: 'o_remove',
      label: 'Cancel',
      fn: removeFn,
      show: showDelete,
      disable: attributes.value?.enroll && isOperateDisabled.value,
      tips: 'You can not cancel the session within 12 hours prior to its starting time',
    },
    {icon: 'o_diversity_1', label: 'Apply to collaborate', fn: collaborateFn, show: showCollaborate, disable: false},
    {icon: 'o_diversity_1', label: 'Apply to collaborate', show: showCollaborateWaiting, disable: true, tips: 'Waiting for approving'},
    {icon: 'o_stop_circle', label: 'End', fn: endFn, show: showEnd, disable: false},
    {icon: 'o_restart_alt', label: 'Reopen', fn: reopenFn, show: showReopen, disable: false},
    {avatar: showToolAvatar, self: isSelfStudy.value, all: isCreator.value || isEnrolled.value || (sessionAttrbutes && !sessionAttrbutes.enroll)},
  ]
}

const goBack = (hash) => {
  let path = route.query.back || '/home/<USER>'
  const query = route.query
  if (query?.back) delete query.back
  if (hash && query.back) {
    path = query.back.replace(/(#\w+)$/, hash)
  }
  //router.replace({path, query})
  router.replace(path)
}

const overviewFn = (o) => {
  let state = isLib.value ? stateMap.LIBARARY : isContents.value ? stateMap.CONTENTS : stateMap.SCHEDULED
  const _attr = getAttributes(o)
  if (_attr.tool) {
    //state = stateMap.TOOL
  }
  router.push({
    path: `/detail/overview/${state}/${o._id}`,
    query: {authId: route.query.authId, back: route.fullPath},
  })
}

const getEvaluation = async () => {
  const student = isCreator.value ? coursession.value.students?.[0] : pub.user._id
  if (student) {
    await App.service('service-rating')
      .find({
        query: {session: coursession.value._id, booker: student},
      })
      .then((res) => {
        evaluationRes.value = res?.data?.[0] ?? {}
      })

    isAccident.value = unsatisfiedOptions.some((e) => e.accident && evaluationRes.value.tags?.includes(e.label))

    if (isAccident.value) {
      await App.service('teaching-accident')
        .find({
          query: {session: coursession.value._id, student},
        })
        .then((res) => {
          accidentRes.value = res?.data?.[0] ?? {}
        })
    }
  }
}

const evaluationForSubstituteFn = async (o) => {
  router.push({path: `/detail/${route.params.type}/${route.params.id}/review`})
}

const evaluationFn = async (o) => {
  //evaluating.value = true
  //await getEvaluation()
  //evaluating.value = false
  if ((isCreator.value && evaluationRes.value?._id) || !isCreator.value) {
    $q.dialog({
      component: EvaluateDialog,
      componentProps: {session: coursession.value, evaluation: evaluationRes.value, accident: accidentRes.value},
      //componentProps: {session: coursession.value, student: pub.user._id},
    }).onOk((obj) => {
      coursession.value.rating = !0
      if (obj.evaluation) {
        evaluationRes.value = obj.evaluation
      }
      if (obj.accident) {
        accidentRes.value = obj.accident
      }
    })
  }
}

const evaluationSubTeacherFn = async (o) => {
  $q.dialog({
    component: EvaluateDialog,
    componentProps: {session: coursession.value, evaluation: evaluationRes.value, accident: accidentRes.value},
  }).onOk((obj) => {
    coursession.value.rating = !0
    if (obj.evaluation) {
      evaluationRes.value = obj.evaluation
    }
    if (obj.accident) {
      accidentRes.value = obj.accident
    }
  })
}

const slidesFn = (o) => {
  router.push({
    path: `/detail/slide/${isLib.value ? stateMap.LIBARARY : isContents.value ? stateMap.CONTENTS : stateMap.SCHEDULED}/${o._id}`,
    query: {authId: route.query.authId, back: route.fullPath},
  })
}

const questionFn = (o) => {
  const query = {}
  if (route.query.authId) {
    query.authId = route.query.authId
  }
  query.back = route.fullPath
  router.push({
    path: `/detail/question/${o._id}`,
    query,
  })
}

const promptsFn = (o) => {
  router.push({
    //path: `/detail/prompts/${isLib.value ? stateMap.LIBARARY : isContents.value ? stateMap.CONTENTS : stateMap.SCHEDULED}/${o._id}`,
    path: `/detail/prompts/edit/${o._id}`,
    query: {back: route.fullPath},
  })
}

const upslidesFn = (o) => {
  slides.pull(o)
}

const rescheduleFn = (o) => {
  router.push({
    path: `/com/reschedule/${o._id}`,
    query: {back: route.fullPath},
  })
}

const editFn = (o) => {
  const _attr = getAttributes(o)
  let id = o._id
  if (isLib.value && o.order?.links?.length) {
    const link = o.order.links.find((item) => item.id == o._id)
    id = link.newId
  }

  router.push({
    path: _attr.tool ? `/account/assessment-tool/${id}` : `/com/${isVideoMode.value ? 'video' : _attr.unit ? 'unit' : 'task'}/edit/${id}`,
    query: {back: route.fullPath},
  })
}

const collaborateFn = (o) => {
  activeCollabItem.value = o
  showInviteDialog.value = true
}

const scheduleFn = (o) => {
  router.push({
    path: `/com/${o.mode}/edit/${o._id}`,
    query: {action: 'schedule', back: route.fullPath},
  })
}

const publishFn = (o) => {
  router.push({
    path: `/com/${o.mode}/edit/${o._id}`,
    query: {action: 'publish', back: route.fullPath},
  })
}

const unpublishFn = (o) => {
  $q.dialog({
    title: `Confirm unpublish`,
    message: `Please confirm that you want to unpublish the content "${o.name ? o.name : 'Untitled'}".`,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    loading.value = true

    if (o.type !== 'selfStudy') {
      await unPublish({_id: o._id})
    } else {
      //self-study
      await App.service('session').patch(o._id, {del: true})
    }
    $q.notify({type: 'info', message: 'Successful'})
    if (contentTab.value == 'me') {
      location.reload()
    } else {
      router.replace('/home/<USER>')
    }
  })
}

const reflectFn = (o, source) => {
  //#5256
  let path = `/reflect/${o._id}`
  const query = {}
  if (isLib.value || source === true) {
    path = `/reflect/lib/${isLib.value ? o._id : o.source}`
  } else if (!attributes.value.content) {
    path = `/reflect/${o.type}/${o._id}`
  }

  if (isSysView.value) {
    query.isSysView = 1
  }

  if (isVerify.value) {
    query.isVerify = 1
  }

  if (route.query.authId) {
    query.authId = route.query.authId
  }

  query.back = route.fullPath
  router.push({path, query})
}

const notesFn = (o) => {
  reflectFn(o, true)
}

const importFn = (o) => {
  router.push({path: `/detail/${route.params.type}/import/${o._id}`})
}

const complaintFn = (o) => {}

const assessingFn = (o) => {}
const correctingFn = (o) => {}

const learningDataFn = (o) => {}
const rosterFn = (o) => {
  toRoom(o, 'd', true)
}

const studentDataFn = (o) => {
  if (!o?._id) return
  router.push(`/account/takeaway/study/${o._id}?studentId=${studentId.value}`)
  /*
  if (studentData.value?._id) {
    //router.push(`/account/takeaway/view/${studentData.value._id}?studentId=${studentId.value}`)
    router.push(`/account/takeaway/study/${coursession.value._id}`)
  } else {
    $q.notify({message: 'The takeaway report hasn’t been generated yet.'})
  }*/
}

const takeawayFn = (o) => {
  if (!o?._id) return
  if (isStudent.value) {
    if (myTakeaway.value?._id) {
      router.push({
        path: `/account/takeaway/${myTakeaway.value.session}/student-view/${pub.user._id}`,
        query: {back: route.fullPath},
      })
    } else {
      $q.notify({message: 'The takeaway report hasn’t been generated yet.'})
    }
  } else if (attributes.value.booking || takeaway.value?._id) {
    router.push({
      path: `/account/takeaway/${o._id}`,
      query: {back: route.fullPath},
    })
  } else {
    generateTakeaway()
  }
}

const archiveFn = (o, del = true) => {
  $q.dialog({
    title: `Confirm ${del ? 'Archive' : 'Restoration'}`,
    message: `Please confirm that you want to ${del ? 'archive' : 'restore'} the content "${o.name ? o.name : 'Untitled'}".`,
    cancel: true,
  }).onOk(async () => {
    loading.value = true
    await patchOneById(o._id, {del: !!del})
    $q.notify({type: 'info', message: 'Successful'})
    goBack()
  })
}

const restoreFn = (o) => {
  archiveFn(o, false)
}

const reopenFn = (o) => {
  $q.dialog({
    title: `Confirm Reopen`,
    message: `Please confirm that you want to reopen the ${attributes.value.course ? 'course' : 'session'}:"${o.name}"?`,
    cancel: true,
  }).onOk(async () => {
    reopening.value = true
    await App.service('session').patch(o._id, {status: 'live'})
    //goBack('#ongoing')
    $q.notify({type: 'positive', message: 'Reopen successfully'})
    window.location.reload()
  })
  /*
  reeditTarget.value = o
  isReopen.value = true
  showReeditDialog.value = true
  */
}

const endFn = (o) => {
  $q.dialog({
    title: `Confirm End`,
    message: `Please confirm that you want to end the ${attributes.value.course ? 'course' : 'session'}:"${o.name}"?`,
    cancel: true,
  }).onOk(async () => {
    ending.value = true
    await App.service('session').patch(o._id, {status: 'close', countdown: {type: 0, deadline: null, down: null}})
    if (!takeaway.value?._id && studentsCount.value) {
      generateTakeaway(true)
    } else {
      goBack('#ended')
    }
    $q.notify({type: 'positive', message: 'Ended successfully'})
  })
}

const generateTakeaway = (back) => {
  $q.dialog({
    message:
      "Would you like to generate the takeaways of this session for students?  Please notice that once takeaways are generated, students' answers will remain consistent with the time of generation and will not be updated further.",
    persistent: true,
    cancel: {
      label: 'Not this time',
      color: 'primary',
      'no-caps': true,
      outline: true,
      rounded: true,
    },
    ok: {
      label: 'Generate now',
      color: 'primary',
      'no-caps': true,
      rounded: true,
    },
  })
    .onOk(async () => {
      loading.value = true
      takeaway.value = await App.service('session-snapshot').get('snapshot', {
        query: {_id: coursession.value._id},
      })
      takeawayFn(coursession.value)
    })
    .onCancel(() => {
      if (back) {
        goBack('#ended')
      }
    })
}

const leaveFn = (o) => {
  if (!collabDocId.value) {
    window.location.reload()
    return
  }
  $q.dialog({
    title: 'Confirm',
    message: `Leave content collaboration: "${o.name}"?`,
    cancel: true,
  }).onOk(async () => {
    leaving.value = true
    await App.service('collab').patch(collabDocId.value, {$pull: {members: {_id: collabMemberId.value}}})
    isCollaborated.value = false
    $q.notify({type: 'info', message: 'Successful'})
    leaving.value = true
  })
}

const policyClick = () => {
  $q.dialog({
    title: 'Cancellation policy',
    message:
      'Workshops<br>Free cancellation within 24 hours before the start of the workshop.<br>Bundled Mentor Service Package<br>Free cancellation of the remaining unused sessions purchased (gift sessions excluded) within 24 hours before the start of the bundled workshop.',
    html: true,
    ok: {
      label: 'I got it ',
      color: 'primary',
      'no-caps': true,
    },
  })
}
async function cancelSession(o) {
  const rs = await App.service('session').patch(o._id, {$pull: {reg: regData.value}})
  $q.notify({type: 'positive', message: 'Cancel enrollment successfully'})
  // o.regNum -= 1
  // regData.value = null
  window.location.reload()
}
const cancelFn = async (o) => {
  $q.dialog({
    title: 'Confirm cancel enrollment',
    message: `Please confirm that you want to cancel enrollment of the ${attributes.value.course ? 'course' : 'session'}: "${
      o.name || serviceBooking.value?.servicePackUser?.snapshot?.name || 'Untitled'
    }"?`,
    cancel: true,
  }).onOk(async () => {
    $q.loading.show()
    if (attributes.value.booking || isInterviewConsultant.value) {
      try {
        await App.service('service-booking').patch('cancel', {
          _id: o.booking,
        })
      } catch (e) {}
      goBack()
    } else {
      if (o.school) {
        return cancelSession(o)
      }
      let resCheck = await App.service('order').get('checkLinks', {
        query: {
          links: [{id: id.value, style: 'session'}],
        },
      })
      if (resCheck?.orderId?.length) {
        let orderId = resCheck?.orderId?.[0]
        await App.service('order').get('cancel', {query: {id: orderId, status: 500}})
        cancelSession(o)
      }
    }
    $q.loading.hide()
  })
}

const enrollFn = async (o) => {
  console.log('enrolling', o)
  enrolling.value = true
  if (!o.school) {
    let resCheck = await App.service('order').get('checkLinks', {
      query: {
        links: [{id: id.value, style: 'session'}],
      },
    })
    if (resCheck?.ordered?.length) {
      router.push({path: `/order/detail/${resCheck?.orderId?.[0]}`, query: {back: route.query.back}})
      return
    }
  }

  if (!validate()) {
    enrolling.value = false
    return
  }
  if (!o.school) {
    if (coursession.value?.servicePack?._id && !orderedService.value.includes(coursession.value?.servicePack?._id)) {
      router.push({
        path: `/order/confirm/pubService/${id.value}`,
        query: {
          back: route.query.back,
          inviteCode: route?.query?.inviteCode,
          isPointMode: route?.query?.isPointMode,
          inviteSource: route?.query?.inviteSource,
          inviteSourceId: route?.query?.inviteSourceId,
          schoolInviter: route?.query?.schoolInviter,
        },
      })
    } else {
      router.push({
        path: `/order/confirm/session/${id.value}`,
        query: {
          back: route.query.back,
          inviteCode: route?.query?.inviteCode,
          isPointMode: route?.query?.isPointMode,
          inviteSource: route?.query?.inviteSource,
          inviteSourceId: route?.query?.inviteSourceId,
          schoolInviter: route?.query?.schoolInviter,
        },
      })
    }
    return
  }

  if (!isLogin.value) {
    return router.push({
      path: '/login',
      query: {role: attributes.value.educator ? 'teacher' : 'student', back: location.pathname + location.search},
    })
  }
  if (attributes.value.educator && !(!pub.user.school && !coursession.value?.school) && !pub.hasSchool(coursession.value?.school)) {
    $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: false,
        type: 'unauthorized',
        title: 'Link no longer available!',
        subtitle: 'You have been removed from the school list',
      },
    })
    return
  }

  $q.dialog({
    title: `Confirm Enroll`,
    message: `Please confirm that you want to enroll the ${attributes.value.course ? 'course' : 'session'} "${o.name}"?`,
    cancel: true,
  }).onOk(async () => {
    //loading.value = true
    $q.loading.show()
    const reg = {avatar: pub.user.avatar, nickname: pub.user.nickname, _id: pub.user._id}
    const rs = await App.service('session').patch(o._id, {_date: new Date(o.start).toString(), $addToSet: {reg}})
    if (!rs?._id) return $q.notify({type: 'negative', message: 'Enrolled unsuccessfully: ' + rs.message})
    regData.value = reg
    $q.notify({type: 'positive', message: 'Enrolled successfully'})
    const range = getStartAndEnd(o.start, 'month')
    if (!attributes.value.educator && !isSelfStudy.value) {
      router.push(
        isLectureRoom.value
          ? `/study/class?tab=lecture&subtab=scheduled&range=${range}`
          : attributes.value.session
            ? `/study/purchased?tab=workshop&subtab=scheduled&type=sessions&range=${range}`
            : `/study/purchased?tab=workshop&subtab=scheduled&type=courses&range=${range}&view=list`
      )
    } else {
      router.replace(
        isSelfStudy.value
          ? '/study/purchased?tab=self&subtab=enrolled'
          : attributes.value.session
            ? `/home/<USER>
            : `/home/<USER>
      )
    }
    $q.loading.hide()
  })
}

const getStartAndEnd = (time, type) => {
  const newDate = new Date(time)
  const startDate = date.startOfDate(newDate, type)
  const endDate = date.endOfDate(newDate, type)

  return `${date.formatDate(new Date(startDate), dateFormat)}~${date.formatDate(new Date(endDate), dateFormat)}`
}

const rmContent = (o) => {
  $q.dialog({
    title: 'Confirm cancel',
    message: `Please confirm that you want to cancel the content "${o.name || serviceBooking?.value?.servicePackUser?.snapshot?.name || 'Untitled'}"?`,
    cancel: true,
  }).onOk(async () => {
    loading.value = true
    await patchOneById(content.value._id, {$pull: {link: {_id: o.linkid}}})
    linkList.value = linkList.value.filter((item) => item._id !== o._id)
    $q.notify({type: 'info', message: 'Cancellation successful'})
  })
}

const cancelBooking = async (o) => {
  let message = 'Please confirm that you want to cancel this booking?'
  const rs = await App.service('service-booking').get(o.booking)
  const diff = date.getDateDiff(new Date(rs.start), new Date(), 'seconds')

  if (diff < 2 * 3600) {
    message = 'Since the current session will start within 2 hours, your cancellation will result in a corresponding penalty. Are you sure you want to cancel?'
  } else {
    message = 'Frequent cancellations of session services can result in a lower ranking of your service offering referrals. Are you sure you want to cancel?'
  }

  $q.dialog({
    title: 'Confirm cancellation',
    message,
    cancel: true,
  }).onOk(async () => {
    if (!rs.cancel) {
      await App.service('service-booking').patch('cancel', {
        _id: o.booking,
      })
    }
    goBack()
  })
}

const doRemove = async (o) => {
  if (isInterviewConsultant.value) {
    cancelBooking(o)
  } else {
    const isArchivedContent = isContents.value && o.del
    $q.dialog({
      title: `Confirm ${isArchivedContent ? 'delete' : 'cancel'}`,
      message: `Please confirm that you want to ${isArchivedContent ? 'delete' : 'cancel'} : "${
        o.name || serviceBooking?.value?.servicePackUser?.snapshot?.name || 'Untitled'
      }"?`,
      cancel: true,
    }).onOk(async () => {
      loading.value = true
      if (attributes.value.content) {
        await removeOneById(o._id)
      } else if (o?.booking) {
        await App.service('service-booking').patch('cancel', {
          _id: o.booking,
        })
      } else {
        await App.service('session').remove(o._id)
      }
      goBack()
      $q.notify({type: 'info', message: 'Successful.'})
    })
  }
}

const removeFn = async (o, item) => {
  if (item?.dialog && item?.disable) {
    const config = {
      message: item.tips,
      cancel: {
        label: 'I got it',
        noCaps: true,
        rounded: true,
        class: 'full-width',
      },
      ok: null,
    }
    if (isPremiumLecture.value) {
      config.ok = {label: 'Cancel', icon: 'o_check', noCaps: true, rounded: true, class: 'col'}
      config.cancel = {label: 'Not now', icon: 'o_arrow_back', outline: true, noCaps: true, rounded: true, class: 'col'}
    }
    $q.dialog(config).onOk(() => {
      doRemove(o)
    })
  } else {
    doRemove(o)
  }
}

const enterclassFn = async (o) => {
  const _attr = getAttributes(o)
  if (_attr.tool) {
    router.push({
      path: `/account/assessment-tool/${o.cid}/${isCreator.value ? 'teacher' : 'student'}/${o.sid}`,
      query: {back: route.fullPath},
    })
  } else {
    toRoom(o, 's')
  }
}

const toRoom = async (link, m, openRoster) => {
  const token = await App.authentication.getAccessToken()
  let host = roomHost
  if (m === 's') {
    router.push({path: `/s/${link.sid}`, query: {back: encodeURIComponent(route.fullPath)}})
  } else {
    window.location.href = `${host}/${m}/${link.sid}?token=${token}${openRoster ? '&roster=1' : ''}&back=${encodeURIComponent(route.fullPath)}`
  }
}

const projectFn = (o) => {
  toRoom(o, 't')
}

const dashboardFn = (o) => {
  toRoom(o, 'd')
}
const duplicateFn = (o) => {
  $q.dialog({
    title: 'Confirm',
    message: `Duplicate content "${o.name}"?`,
    cancel: true,
  }).onOk(async () => {
    duplicating.value = true
    let name = o.name + ' - Copy 1'
    if (!isNaN(parseInt(o.name?.match(/ Copy (\d)$/)?.[1]))) {
      let copyText = ' Copy ' + (parseInt(o.name?.match(/ \d+$/)?.[0]) + 1)
      name = o.name.replace(/ Copy \d+$/, copyText)
    }

    await copyOne({_id: o._id, name})
    $q.notify({type: 'info', message: 'Successful'})
    router.replace('/home/<USER>')
    //goBack()
  })
}

const getAttributes = (item, key) => {
  let obj = {}
  if (props.isMyPublished) {
    obj = contentsType[item.mode || item.type.substr(0, 4)]
  } else {
    obj = contentsType[item.mode || item.type]
  }
  if (key) {
    return obj?.[key]
  } else {
    return obj
  }
}

const ratingChartOptions = ref({
  indexAxis: 'y',
  responsive: true,
  plugins: {
    legend: {
      display: false,
    },
    title: {
      display: true,
      text: 'Ratings',
    },
  },
})

const ageChartOptions = ref({
  responsive: true,
  plugins: {
    legend: {
      display: false,
    },
    title: {
      display: true,
      text: 'Ages used with',
    },
  },
})

const cartFn = async () => {
  console.log('<========33cartFn', id.value)

  if (!validate()) return
  if (isInCart.value) {
    router.push('/order/cart')
  } else {
    let style = undefined
    if (isSelfStudy.value) {
      style = 'session'
    } else if (isLib.value) {
      style = 'unit'
    }
    await App.service('cart')
      .create({
        goodsId: id.value,
        style,
        inviter: route?.query?.inviteCode,
        inviteSource: route?.query?.inviteSource,
        inviteSourceId: route?.query?.inviteSourceId,
        schoolInviter: route?.query?.schoolInviter,
      })
      .then(() => {
        $q.dialog({
          component: AddCartDialog,
          componentProps: {
            goBack: goBack,
            content: style == 'unit' ? content.value : coursession.value,
          },
        })
      })
      .catch((err) => {
        $q.notify({type: 'negative', message: err.message})
      })
  }
}

const copyFn = async (o) => {
  if (!validate()) return

  $q.dialog({
    title: `Confirm copy`,
    message: `Please confirm that you want to copy the session "${content?.value.name}"?`,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    copying.value = true
    const rs = await copyOne({_id: id.value, orderId: o.order._id})
    if (!rs) $q.notify({type: 'negative', message: 'Copied unsuccessfully'})
    else $q.notify({type: 'positive', message: 'Copied successfully'})
    router.push({path: `/home/<USER>
  })
}

const buyFn = async () => {
  console.log('buying')
  let resCheck = await App.service('order').get('checkLinks', {
    query: {
      links: [{id: id.value, style: 'unit'}],
    },
  })
  if (resCheck?.ordered?.length) {
    router.push({path: `/order/detail/${resCheck?.orderId?.[0]}`, query: {back: route.query.back}})
    return
  }

  if (!validate()) return
  router.push({
    path: `/order/confirm/unit/${id.value}`,
    query: {
      back: route.query.back,
      inviteCode: route?.query?.inviteCode,
      isPointMode: route?.query?.isPointMode,
      inviteSource: route?.query?.inviteSource,
      inviteSourceId: route?.query?.inviteSourceId,
      schoolInviter: route?.query?.schoolInviter,
    },
  })
}

const collectFn = async () => {
  if (!validate()) return
  collecting.value = true
  if (content.value?.collected) {
    const doc = await App.service('collect').remove(content.value?.collected)
    content.value.collected = null
  } else {
    const doc = await App.service('collect').create({rid: id.value, type: content.value.mode})
    content.value.collected = doc._id
  }
  collecting.value = false
}

const unitTypeOptions = ref([
  {label: 'All', value: 'all'},
  {label: 'Single-subject Unit', value: 'Single'},
  {label: 'UOI', value: 'UOI'},
  {label: 'IDU', value: 'IDU'},
])

const getUnitType = (ut) => {
  return unitTypeOptions.value.find((item) => item.value === ut)
}

const checkCollaboration = async () => {
  const {_id, members} = await App.service('collab').get(id.value, {query: {type: content.value.mode}})
  collabDocId.value = _id
  members.forEach((member) => {
    if (member.email === pub.user.email && member.status == true && member.role == 'write') {
      isWriterRole.value = true
    }
    if (member.email === pub.user.email && member.status == true) {
      isCollaborated.value = true
    }
    if (member.email === pub.user.email) {
      collabMemberId.value = member._id
    }
  })
  findCollab()
}

const findCollab = async () => {
  collabs.value = await App.service('collab').get('byRid', {query: linkList.value.map((v) => v.id)})
}

const getRelateList = async () => {
  linkList.value = []
  if (isContents.value || isLib.value) {
    let rs = null
    if (snapshot.value?.linkSnapshot) {
      rs = Object.values(snapshot.value.linkSnapshot)
    } else {
      rs = await relateLinkList({rid: id.value})
    }
    content.value.link.forEach((link, index) => {
      let meta = rs.filter((item) => item._id == link.id)
      if (meta.length) {
        linkList.value[index] = {...meta[0], ...{linkid: link._id, group: link.group, id: link.id, main: true}}
      }
    })
  } else if (coursession.value) {
    const _ids = []
    let sessions = {}
    if (isCoursePage.value) {
      if (coursession.value.sessionType == 'live') {
        sessions = await App.service('session').find({query: {pid: id.value, status: {$exists: true}, $limit: 1000}})
      } else {
        return
      }
      const childs = []
      sessions.data?.forEach(({_id, cid, sid, pidGroup}) => {
        const link = coursession.value.task?.link.find((e) => e.id == cid)
        childs.push({_id, cid, group: link.group, sid})
      })
      coursession.value.childs = childs
      coursession.value.childSize = childs.length
    } else {
      groups.value = coursession.value.task.linkGroup
      sessions = await App.service('session').find({
        query: {_id: {$in: coursession.value.childs.map((item) => item._id)}, status: {$exists: true}, $limit: 1000},
      })
    }

    const sessionsData = sessions.data.sort((a, b) => {
      return new Date(a.start) - new Date(b.start)
    })

    const courseChilds = coursession.value.childs.reduce(function (r, a) {
      if (a.group) {
        r[a.group] = r[a.group] || []
        r[a.group].push(a)
      }
      return r
    }, Object.create(null))

    Object.values(courseChilds).forEach((group) => {
      const temp = []
      group.forEach((item) => {
        const itemDetail = sessionsData.find((i) => i._id == item._id)
        if (itemDetail?.pid == id.value) {
          temp.push({...item, ...itemDetail, ...{main: true}})
        }
        const childs = sessionsData.filter((c) => c.pid == item._id)
        childs.forEach((cld) => {
          cld.group = item.group
          cld.groupName = item.groupName
          temp.push(cld)
        })
      })
      groups.value?.push(temp)
    })

    const _groups = []
    const _allSessions = []
    groups.value.forEach((group) => {
      if (!group?.length) return
      const sessions = []
      const mainSessions = group
        .filter((item) => item.pid == id.value)
        .sort((a, b) => {
          return new Date(a.start) - new Date(b.start)
        })
      mainSessions.forEach((session) => {
        sessions.push(session)
        _allSessions.push(session)
        const subSessions = group.filter((item) => item.pid == session._id)
        sessions.push(...subSessions)
        _allSessions.push(...subSessions)
      })
      _groups.push({name: group[0]['groupName'], _id: group[0]['group']})
    })

    groups.value = coursession.value.task.linkGroup
    linkList.value = _allSessions
  } else {
    console.log('<==============uncategory===================>')
  }

  if (isLimit.value) {
    uniqueList.value = [...new Set(linkList.value.map((item) => item._id))].map((id) => linkList.value.find((item) => item?._id === id))
  }
}

const getItemStatus = (link) => {
  let text = ''
  const now = new Date()
  if (new Date(link.start) > now) {
    text = 'Scheduled'
  } else if (new Date(link.start) < now && link.status !== 'close') {
    text = 'Ongoing'
  } else if (link.del) {
    text = 'Archived'
  } else if (link.status == 'close') {
    text = 'Ended'
  }

  return text
}

const getItemById = (id, obj) => {
  for (const key in obj) {
    if (obj[key]._id === id) {
      return obj[key]
    }
  }
  return null // Return null if not found
}

const bindInviter = async () => {
  if (pub?.user?._id && route?.query?.inviteCode) {
    await App.service('users').patch('bindInviter', {inviter: route?.query?.inviteCode})
  }
}

const main = async () => {
  bindInviter()
  $q.loading.hide()
  if (props.id) {
    id.value = props.id
  }
  if (!id.value) return
  loading.value = true

  await getAuthList(true, {$skip: 0, $limit: 2000})

  let contentId = id.value
  const query = {isLib: isLib.value}
  if (isContents.value || isLib.value) {
    await getServiceAuth()
    await getPremiumLecture()

    if (snapshot.value?._id) {
      content.value = snapshot.value.unitSnapshot._id == id.value ? snapshot.value.unitSnapshot : getItemById(id.value, snapshot.value.linkSnapshot)
      content.value.owner = snapshot.value.owner
    } else {
      try {
        content.value = await getOneById(id.value, {query})
      } catch (e) {
        isUnavailable.value = true
      }
    }

    if (content.value?._id) {
      attributes.value = getAttributes(content.value)
    }

    isCreator.value = pub.user._id === content.value?.uid
    isAuthor.value = pub.user._id === content.value?.uid

    if ((isLib.value && !content.value?.publish?.lib && !content.value?.publish?.link) || (!isCreator.value && content.value?.del)) {
      isUnavailable.value = true
    }

    const isFromOrderDetail = route.query.back?.includes('/order/detail/')

    if (isUnavailable.value) {
      $q.dialog({
        component: PromptPage,
        componentProps: {
          fullscreen: !isFromOrderDetail,
          persistent: isFromOrderDetail,
          type: isFromOrderDetail ? '' : 'unavailable',
          cancel: !isFromOrderDetail,
          logo: !isFromOrderDetail,
          title: isFromOrderDetail ? 'The products/session are no longer available' : '',
          back: true,
        },
      })
      return
    }

    if (isContents.value && content.value.source) {
      try {
        source.value = await getOneById(content.value.source, {query: {isLib: true}})
      } catch (e) {
        console.warn(e)
      }
    }
    groups.value = content.value.linkGroup
  } else {
    try {
      coursession.value = await App.service('session').get(id.value)

      attributes.value = getAttributes(coursession.value)
    } catch (e) {
      $q.dialog({
        component: PromptPage,
        componentProps: {
          fullscreen: true,
          type: 'unavailable',
          back: true,
        },
      })
      return
    }

    isCreator.value = pub.user._id === coursession.value.uid
    isAuthor.value = pub.user._id === coursession.value.task?.uid
    isInTheSameSchool.value = coursession.value.school && pub.hasSchool(coursession.value.school)

    if (pub.user?._id && (attributes.value.session || attributes.value.course) && coursession.value.sid) {
      await findRooms()
      await findClasses()
    }

    if (route.query.bid) {
      try {
        booking.value = await App.service('service-booking').get(route.query.bid)
      } catch (e) {}
    }

    //TODO:
    if (!validate()) {
      return
    }

    await getServicePackAndUser()
    await getServiceBooking()

    if (coursession.value.sessionType == 'live' && coursession.value.pid) {
      isCoursePage.value = true
    }

    if (isCoursePage.value) {
      course.value = await App.service('session').get(coursession.value.pid)
      courseRegData.value = course.value.regData
      const courseAttributions = getAttributes(course.value)
      isParentCoursePublic.value = !!courseAttributions.public
    }

    if (route.hash == '#activity') {
      getRelevant()
    }
    const type = coursession.value.type
    isTaskWorkshop.value = type == 'taskWorkshop'

    content.value = coursession.value?.task
    deadline.value = coursession.value.regDate
    contentId = coursession.value.cid
    regData.value = coursession.value.regData

    if (attributes.value.self) {
      isSelfStudy.value = true
    }

    if (coursession.value.regNum && coursession.value.regNum >= coursession.value.discount?.size) {
      getEnrolledMembers()
    }
  }

  if (attributes.value.service) {
    if (Array.isArray(content.value?.outlineSubjects)) {
      subjects.value = content.value.outlineSubjects.map((e) => ({label: e, value: e}))
    }
  } else if (content.value.curriculum) {
    const curr = curriculum.pubList[content.value.curriculum] ?? 'Others'
    subjects.value.push({label: curr, value: curr})
  }
  if (attributes.value.tool) {
    loading.value = false
    return
  }

  if (['task', 'pdTask'].includes(content.value.mode)) {
    isTaskMode.value = true
  }

  if (content.value.mode === 'video') {
    isVideoMode.value = true
  }

  if (content.value.video) {
    isAvlVideo.value = true
  }

  if (['unit', 'pdUnit'].includes(content.value.mode)) {
    isUnitMode.value = true
  }

  if (isContentView.value) {
    outline.value = await App.service('task-outline').get('byRid', {query: {_id: contentId}})
  } else if (content.value) {
    //发布和schedule的数据都不需要请求接口
    outline.value = content.value.outline
  }
  if (outline.value && !(!isCreator.value && isScoreEnabled.value)) {
    //https://github.com/zran-nz/bug/issues/5517
    /*
    subjects.value.push(
      ...[
        ...getAllNameAndCurr(outline.value.outline?.data),
        ...getAllNameAndCurr(outline.value.pd?.data),
        ...getAllNameAndCurr(outline.value.skills?.data),
        ...getAllNameAndCurr(outline.value.assess?.data),
      ]
    )
    */
  }

  if (content.value?.subjects?.length) {
    subjects.value.push(...content.value.subjects)
  }
  getUniqueSubjectsByValue()

  if (!props.isImport) {
    getReview()
    getStat()
    await getRelateList()
    if (isContentView.value && isUnitMode.value) {
      //await getSubLinksOfAll()
    }
  }

  if (pub.user._id) {
    if (isContentView.value) {
      gradeOptions.value = await curriculum.gradeOptions(pub.user?.school || pub.user._id)
    } else if (coursession.value?.school || coursession.value?.uid) {
      gradeOptions.value = await curriculum.gradeOptions(coursession.value?.school || coursession.value?.uid)
    }
    checkInCart()
  }

  if (!isCreator.value && isContents.value && !snapshot.value?._id) {
    checkCollaboration()
  }

  //TODO TEST

  if (attributes.value.type == 'booking' && coursessionStatus.value == 'Ended') {
    await getEvaluation()
    if (!(!coursession.value.rating && new Date() - new Date(coursession.value.end) > 24 * 3600 * 1000)) {
      if (route.query.evaluation) {
        evaluationFn()
      }
    }
  }

  if (route.query.isPointMode) {
    isPointMode.value = true
    await pStore.getClaimSetting()
  }

  collectAllTags()
  await getTakeaway()
  await getStudentData()
  await getMyTakeaway()
  await getOrderedService()
  await pStore.getIncomeSetting()

  loading.value = false
}

const getOrderedService = async () => {
  const rs = await App.service('order').get('orderedService', {query: {buyer: pub.user._id}})
  orderedService.value = rs?.serviceIds || []
}

const getServiceAuth = async () => {
  if (route.query.authId) {
    await App.service('service-auth')
      .get(route.query.authId)
      .then((res) => {
        if (res?._id) {
          serviceAuth.value = res
          snapshot.value = res
          if (serviceAuth.value.approval) {
            getAudior(serviceAuth.value.approval)
          }
        }
      })
  }
}

const getPremiumLecture = async () => {
  if (route.query.spuid) {
    await App.service('service-pack-user')
      .get(route.query.spuid)
      .then((res) => {
        if (res?.snapshot) {
          snapshot.value = res.snapshot
        }
      })
  }
}

const getAudior = async (approval) => {
  if (approval?._id) {
    auditor.value = approval
    console.log('approval', approval)
  }
  // await App.service('users')
  //   .get(follower)
  //   .then((res) => {
  //     if (res?._id) {
  //       console.log('res', res)
  //       auditor.value = res
  //     }
  //   })
}

const getStudentData = async () => {
  if (coursession.value?._id && studentId.value) {
    try {
      const rs = await App.service('session-snapshot').get('studentData', {query: {_id: coursession.value._id, student: studentId.value}})
      if (rs?.pages?.length) {
        studentData.value = rs
      }
    } catch (e) {
      console.log(e)
    }
  }
}

const getTakeaway = async () => {
  //TODO: && pub?.user?._id
  if (attributes.value.session && coursession.value?._id) {
    try {
      takeaway.value = await App.service('session-snapshot').get(coursession.value._id)
    } catch (e) {
      console.log(e)
    }
  }
}

const getMyTakeaway = async () => {
  if (isStudent.value && attributes.value.session && coursession.value?._id) {
    try {
      const rs = await App.service('session-takeaway-snapshot').find({query: {session: coursession.value._id, uid: pub.user._id}})
      if (rs?.data?.length && rs.data[0].hash) {
        myTakeaway.value = rs.data[0]
      }
    } catch (e) {
      console.log(e)
    }
  }
}

const validate = () => {
  if (!pub.user._id) {
    router.push({path: '/login', query: {back: location.pathname + location.search}})
    return false
  }

  let authorized = false
  if (isContents.value || isLib.value) {
    if (isStudent.value && isLib.value) {
      console.warn('<======The content of the library is not accessible to students.')
    } else if (isLib.value) {
      authorized = true
    }
  } else {
    if (isCoTeacher.value || booking.value?.oldSession?._id == id.value) {
      //https://github.com/zran-nz/bug/issues/5123#issuecomment-2378814236
      console.log('<======taught by others #4264 4.a')
      authorized = true
    } else {
      if (isInTheSameSchool.value || !coursession.value?.school) {
        if (
          (attributes.value.educator && !isStudent.value) ||
          (!attributes.value.educator && ((isStudent.value && !coursession.value.classId) || isInTheStudentsList.value || isInTheSameClass.value))
        ) {
          authorized = true
        } else {
          console.warn('<======for educator:', attributes.value.educator, 'you are educator:', !isStudent.value)
          if (!isInTheSameClass.value) {
            console.warn('<======You are not in the some class')
          }
          if (!isInTheStudentsList.value) {
            //for personal class
            console.warn('<======You are not in the students lists')
          }
        }
      } else {
        if (!isInTheSameSchool.value) {
          console.warn('<======You are not in the some school')
        }
      }
    }
  }

  if (isSubrole()) {
    return true
  }

  if (!authorized && !isCreator.value) {
    return true
    $q.dialog({
      component: PromptPage,
      componentProps: {
        //fullscreen: true,
        type: 'unauthorized',
        title: 'You have no access to the page',
        persistent: true,
        back: true,
      },
    })
    return false
  } else {
    return true
  }
}

const isSubrole = () => {
  return subRole(coursession.value, route.query.subform, pub.user._id)
}

const checkInCart = async () => {
  const query = {
    $limit: 10,
    $sort: {updatedAt: -1},
    goodsId: {
      $in: [id.value],
    },
  }
  const res = await App.service('cart').find({query})
  if (res?.total > 0) {
    isInCart.value = true
  }
  // console.log('res', res)
}

const getServiceBooking = async () => {
  if (coursession.value.booking) {
    try {
      await App.service('service-booking')
        .get(coursession.value.booking)
        .then((res) => {
          if (res?._id) {
            serviceBooking.value = res
            const snapshot = res?.servicePackUser?.snapshot
            if (['interview', 'interviewTeacher'].includes(snapshot?.consultant?.type)) {
              isInterviewConsultant.value = true
            }
            if (snapshot?.consultant?.type == 'carer') {
              isCarerConsultant.value = true
            }
            if (snapshot?.contentOrientatedEnable === false && snapshot?.serviceRoles == 'mentoring') {
              is1V1MentorAndContentOrientatedNotEnable.value = true
            }
            if (res?.servicePackUser?.pid) {
              isPremiumLecture.value = true
            }
          }
        })

      await App.service('service-booking')
        .get('lectureLastEnd', {query: {_id: coursession.value.booking}})
        .then((res) => {
          if (res?._id) {
            lastLecture.value = res
          }
        })
    } catch (e) {
      console.warn(e)
    }
  }
}

const getServicePackAndUser = async () => {
  if (coursession.value.servicePack?._id) {
    try {
      await App.service('service-pack')
        .get(coursession.value.servicePack._id)
        .then((res) => {
          if (res?._id) {
            console.log(res, '<====================servicePack')
            servicePack.value = res
            servicePack.value.servicePack = coursession.value.servicePack
          }
        })
    } catch (e) {
      console.warn(e)
    }

    if (!isCreator.value) {
      try {
        await App.service('service-pack-user')
          .find({query: {uid: pub.user._id, 'session._id': coursession.value._id}})
          .then((res) => {
            servicePackUser.value = res?.data?.[0] ?? {}
            console.log(servicePackUser.value, '<=================servicePackUser')
            if (!servicePackUser.value?._id) {
              console.warn(res, 'It could be an issue caused by outdated data. Please try using the latest data for troubleshooting.<=====')
            }
          })
      } catch (e) {
        console.warn(e)
      }
    }
  }
}

const getRelevant = async () => {
  if (coursession.value.pid) {
    relevant.value = await App.service('session').get(coursession.value.pid)
  }
}

const findRooms = async () => {
  roomStat.value = await App.service('rooms').get(coursession.value.sid)
  //https://github.com/zran-nz/bug/issues/4960#issue-2371590837
  //isCoTeacher.value = roomStat.value?.teachers?.some((e) => e._id == pub.user._id)
}

const findClasses = async () => {
  if (pub.user?.schoolInfo?._id) {
    classesList.value = await App.service('school-user').get('classList', {query: {school: pub.user.schoolInfo._id}})
  } else {
    classesList.value = await App.service('classes').find({query: {del: false}})
  }

  if (classesList.value?.length) {
    isInTheSameClass.value = classesList.value?.some((e) => e._id == coursession.value?.classId)
  }
}

const collectAllTags = () => {
  const o = content.value
  const tags = []
  if (o?.type) {
    tags.push(getUnitType(o.type)?.label || o.type)
  }
  if (o?.grades && Array.isArray(o.grades)) {
    o.grades.forEach((item) => {
      tags.push(item['value'])
      if (item.label) {
        tags.push(item.label)
      }
    })
  }
  if (o?.sessionType) {
    tags.push(o.sessionType == 'live' ? 'Live' : 'Self-study')
  }

  if (linkList.value.length) {
    //tags.push(`${linkList.value.length} ${isContents.value ? 'content' : 'session'}${linkList.value.length > 1 ? 's' : ''}`)
  }
  if (calculateDuration()) {
    //tags.push(calculateDuration())
  }

  subjects.value = [
    ...subjects.value,
    ...tags.map((item) => {
      return {value: item, label: item}
    }),
  ]
}

const getUniqueSubjectsByValue = () => {
  const uniqueSubjects = subjects.value.reduce((unique, item) => {
    const found = unique.find((obj) => obj.value === item.value)
    if (!found) {
      unique.push(item)
    }
    return unique
  }, [])

  subjects.value = uniqueSubjects
}

const getAllNameAndCurr = (dataObject) => {
  const result = []
  if (!dataObject) {
    return result
  }
  const values = Object.values(dataObject)
  for (let i = 0; i < values.length; i++) {
    const label = values[i].curr
    const value = values[i].name
    if (value) {
      result.push({label, value})
    }
  }
  return result
}

const getReview = async () => {
  const rs = await App.service('reviews').find({query: {rid: id.value}})
  if (rs?.data?.length) {
    myReview.value = rs.data[0]
    ageRange.value.min = myReview.value.grade[0]
    ageRange.value.max = myReview.value.grade[1]
    myRating.value = myReview.value.rate
    myNote.value = myReview.value.note
  }
}

const getStat = async () => {
  const rs = await App.service('reviews').get('stat', {query: {rid: id.value}})
  if (rs) {
    topRating.value = rs.rate ?? 0
    ageChartData.value.labels = Object.keys(rs.grades)
    ageChartData.value.datasets.push({data: Object.values(rs.grades)})
    ratingChartData.value.datasets.push({data: Object.values(rs.rates).reverse()})
  }
}

const getSubLinksOfAll = async () => {
  const linkIdsExcludeTool = []
  linkList.value.forEach((item) => {
    if (item.mode === 'task') {
      linkIdsExcludeTool.push(item.id)
    }
  })
  const rs = await allRelateLinkList({rid: linkIdsExcludeTool})
  const newLinkList = []
  linkList.value.forEach((item, index) => {
    newLinkList.push({...item, ...{main: true}})
    if (rs[item.id]?.length) {
      rs[item.id].forEach((c) => {
        newLinkList.push({...c, ...{group: item.group, pid: item._id}})
      })
    }
  })
  linkList.value = newLinkList
}

const getSubLinksOf = (link) => {
  return linkList.value.filter((item) => {
    const toolCount = isContentView.value ? item.toolCount : item.task?.toolCount
    return item.pid == link._id && !(!isCreator.value && !toolCount?.self && !toolCount?.others)
  })
}

const getLinksByGroup = (group) => {
  return linkList.value.filter((item) => {
    const toolCount = isContentView.value ? item.toolCount : item.task?.toolCount
    const _attr = getAttributes(item)
    return item.group == group._id && !(_attr.tool && !isCreator.value && !toolCount?.self && !toolCount?.others)
  })
}

const getMainLinksByGroup = (group) => {
  return linkList.value.filter((item) => {
    const toolCount = isContentView.value ? item.toolCount : item.task?.toolCount
    const _attr = getAttributes(item)
    return item.group == group._id && item.main && !(_attr.tool && !isCreator.value && !toolCount?.self && !toolCount?.others)
  })
}

const getMainLinksLength = () => {
  let _mainLinks = []
  _mainLinks = linkList.value.filter((link) => link.main)
  return _mainLinks.length
}

const calculateDuration = (item) => {
  let prefix = 'Duration: '
  let target = null
  if (isContentView.value || item) {
    target = item ?? content.value
    if (target.duration?.value && target.duration?.unit) {
      return `${prefix} ${target.duration?.value} ${target.duration?.unit}${target.duration?.value > 1 ? 's' : ''}`
    } else if (target.end || (target.countdown?.type == 1 && target.countdown?.deadline)) {
    } else if (target.countdown?.type == 2 && target.countdown?.down) {
      prefix = 'Countdown: '
      return `${prefix} ${target.countdown?.down} mins`
    } else {
      return false
    }
  } else {
    target = coursession.value
  }

  if (target && target.start && (target.end || target.countdown?.deadline)) {
    //#4915
    const end = target.end || target.countdown?.deadline
    const duration = new Date(end) - new Date(target.start)
    const hours = Math.floor(duration / (60 * 60 * 1000))
    const minutes = Math.floor((duration % (60 * 60 * 1000)) / (60 * 1000))

    let result = ''

    if (hours > 1) {
      result += `${hours} hours `
    } else if (hours === 1) {
      result += `${hours} hour `
    }

    if (minutes > 1) {
      result += `${minutes} mins`
    } else if (minutes === 1) {
      result += `${minutes} min`
    }

    return `${prefix} ${result.trim()}`
    /*
    const durationInMs = new Date(end) - new Date(target.start)
    const hours = Math.ceil(durationInMs / 3600000)
    const days = Math.floor(hours / 24)
    const weeks = Math.floor(days / 7)
    const remainingDays = days % 7
    const remainingHours = hours % 24

    const result = []

    if (weeks > 0) {
      result.push(`${weeks} week${weeks > 1 ? 's' : ''}`)
    }

    if (remainingDays > 0) {
      result.push(`${remainingDays} day${remainingDays > 1 ? 's' : ''}`)
    }

    if (remainingHours > 0) {
      result.push(`${remainingHours} hour${remainingHours > 1 ? 's' : ''}`)
    }

    return `${prefix} ${result.join(' ')}`
    */
  } else {
    return null
  }
}

const addOrPatchReview = async () => {
  const query = {
    rate: myRating.value,
    grade: [ageRange.value.min, ageRange.value.max],
    note: myNote.value,
  }
  if (myReview.value) {
    await App.service('reviews').patch(myReview.value._id, query)
  } else {
    await App.service('reviews').create({rid: id.value, ...query})
  }

  editComment.value = false
}

const onStartTimeChanged = (val) => {
  startTimeInRange.value = val
}

const onSessionTimeLoaded = (val) => {
  loadingTime.value = false
  sessionTimeState.value = val
}

const onChildSessionTimeLoaded = async (val, item) => {
  item._status = getItemStatus(item)
}

watch(
  () => route.path,
  () => {
    if (route.path.indexOf('/content/') !== 0) return
    main()
  }
)
watch(
  () => route.params.id,
  (val) => {
    window.location.reload()
  }
)
onMounted(main)
</script>
<style lang="sass" scope>
.explicit-page-container
  .explicit-page-actions-list
    .q-item
      &[aria-expanded]
        display: none
  .q-img__content
    div
      background: rgba(0, 0, 0, 0)
  .explicit-page-enrolled-data
    .q-item
      padding:0
      min-height: 37px
  .explicit-page-min-height
    min-height: 65px
  .assessing-status
    .q-item__label
      padding-bottom: 6px
</style>
