/* eslint-env node */

/*
 * This file runs in a Node context (it's NOT transpiled by Babel), so use only
 * the ES6 features that are supported by your Node version. https://node.green/
 */

// Configuration for your app
// https://v2.quasar.dev/quasar-cli-vite/quasar-config-js

// const {configure} = require('quasar/wrappers')
import {defineConfig} from '#q-app/wrappers'
import {vueI18n} from '@intlify/vite-plugin-vue-i18n'
import checker from 'vite-plugin-checker'
import {fileURLToPath} from 'node:url'
// const path = require('path')
import {exec} from 'child_process'

const mode = process.argv[3] ?? 'spa'
const port = {spa: 9003, ssr: 9004, pwa: 9005}[mode]
console.log('quasar port:', port, process.argv, process.env.NO_CHECKER)
const prr = []
if (!process.env.NO_CHECKER) {
  // 开发服务上不启用
  prr.push(
    // checker({
    //   enableWorker: false, // 禁用 Worker 模式
    //   terminal: false,
    //   overlay: {
    //     initialIsOpen: true,
    //     position: 'br',
    //   },
    //   eslint: {
    //     useFlatConfig: true,
    //     // lintCommand: 'eslint -c ./eslint.config.js "./src/App.vue"',
    //     lintCommand: 'eslint -c ./eslint.config.js "./src*/**/*.{js,mjs,cjs,vue}"',
    //   },
    // }),
    {
      name: 'track-files',
      configureServer(server) {
        server.watcher.on('change', (file) => {
          // 标准化路径分隔符
          const normalizedFile = file.replace(/\\/g, '/')
          if (normalizedFile.match(/src\/.*\.(js|ts|vue|jsx|tsx)$/) && !file.includes('node_modules')) {
            const command = `eslint -c ./eslint.config.js "${file}"`
            exec(command, (err, stdout, stderr) => {
              if (!err) return console.info(`${file} is ok`)
              console.error(stderr || stdout)
            })
          }
        })
      },
    }
  )
}
export default defineConfig((ctx) => {
  return {
    supportTS: true,
    eslint: {
      // fix: true,
      // include = [],
      // exclude = [],
      // rawOptions = {},
      warnings: true,
      errors: true,
    },

    // https://v2.quasar.dev/quasar-cli/prefetch-feature
    // preFetch: true,

    // app boot file (/src/boot)
    // --> boot files are part of "main.js"
    // https://v2.quasar.dev/quasar-cli/boot-files
    boot: ['i18n', {path: 'pub', server: true}],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#css
    css: ['app.scss'],

    // https://github.com/quasarframework/quasar/tree/dev/extras
    extras: [
      // 'ionicons-v4',
      // 'mdi-v5',
      'fontawesome-v6',
      // 'eva-icons',
      // 'themify',
      // 'line-awesome',
      // 'roboto-font-latin-ext', // this or either 'roboto-font', NEVER both!

      'roboto-font', // optional, you are not bound to it
      'material-icons', // optional, you are not bound to it
      'material-icons-outlined', // optional, you are not bound to it
    ],

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#build
    build: {
      target: {
        browser: ['es2019', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
        node: 'node20',
      },
      sourceMap: false,
      vueRouterMode: 'history', // available values: 'hash', 'history'
      // vueRouterBase,
      // vueDevtools,
      // vueOptionsAPI: false,

      // rebuildCache: true, // rebuilds Vite/linter/etc cache on startup

      publicPath: '/v2/',
      // analyze: true,
      // env: {},
      // rawDefine: {}
      // ignorePublicFolder: true,
      // minify: false,
      // polyfillModulePreload: true,
      // distDir

      // extendViteConf (viteConf) {},
      // viteVuePluginOptions: {},

      vitePlugins: [
        vueI18n({
          // if you want to use Vue I18n Legacy API, you need to set `compositionOnly: false`
          compositionOnly: false,
          runtimeOnly: false,
          // you need to set i18n resource including paths !
          // include: path.resolve(__dirname, './src/i18n/**'),
          include: [fileURLToPath(new URL('./src/i18n', import.meta.url))],
        }),
        ...prr,
      ],
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#devServer
    devServer: {
      allowedHosts: [
        'all',
        'dev.classcipe.com',
        'test.classcipe.com',
        'classcipe.cn',
        'dev.classcipe.cn',
        'devroom.classcipe.cn',
        'test.classcipe.cn',
        'testroom.classcipe.com',
        'dev.nat',
        '36.acanss.com',
      ],
      middlewareMode: false,
      // https: true,
      https: false,
      port,
      // base: '/v2/',
      hmr: {
        path: 'hmr',
        port: port + 10,
        clientPort: process.argv[3] ? null : 443,
      },
      proxy: {
        '/classcipe': {
          target: 'https://dev.classcipe.com/',
          // target: 'https://classcipe.com/',
          ws: false,
          changeOrigin: true,
          rewrite: null,
        },
        '/fio': {
          target: process.argv[4] ? `http://${process.argv[4]}:3030/` : 'https://dev.classcipe.com/',
          // target: process.argv[4] ? 'http://**********:4000/' : 'https://classcipe.com/',
          ws: true,
          changeOrigin: true,
          rewrite: process.argv[4] ? (path) => path.replace(/^\/fio/, '') : null,
        },
      },
      cors: true, // opens browser window automatically
      open: false, // opens browser window automatically
    },

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#framework
    framework: {
      cssAddon: true,
      config: {
        notify: {
          /* look at QuasarConfOptions from the API card */
          position: 'bottom-left',
          type: 'positive',
        },
        screen: {
          bodyClasses: true, // <<< add this
        },
      },

      // iconSet: 'material-icons', // Quasar icon set
      // lang: 'en-US', // Quasar language pack

      // For special cases outside of where the auto-import strategy can have an impact
      // (like functional components as one of the examples),
      // you can manually specify Quasar components/directives to be available everywhere:
      //
      // components: [],
      // directives: [],

      // Quasar plugins
      plugins: ['Notify', 'AppFullscreen', 'Dialog', 'Loading', 'BottomSheet', mode === 'ssr' ? 'Meta' : undefined],
    },

    // animations: 'all', // --- includes all animations
    // https://v2.quasar.dev/options/animations
    animations: [],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#property-sourcefiles
    // sourceFiles: {
    //   rootComponent: 'src/App.vue',
    //   router: 'src/router/index',
    //   store: 'src/store/index',
    //   registerServiceWorker: 'src-pwa/register-service-worker',
    //   serviceWorker: 'src-pwa/custom-service-worker',
    //   pwaManifestFile: 'src-pwa/manifest.json',
    //   electronMain: 'src-electron/electron-main',
    //   electronPreload: 'src-electron/electron-preload'
    // },

    // https://v2.quasar.dev/quasar-cli/developing-ssr/configuring-ssr
    ssr: {
      // ssrPwaHtmlFilename: 'offline.html', // do NOT use index.html as name!
      // will mess up SSR

      // extendSSRWebserverConf (esbuildConf) {},
      // extendPackageJson (json) {},

      pwa: false,

      // manualStoreHydration: true,
      // manualPostHydrationTrigger: true,

      prodPort: 3000, // The default port that the production server should use
      // (gets superseded if process.env.PORT is specified at runtime)

      middlewares: [
        'render', // keep this as last one
      ],
    },

    // https://v2.quasar.dev/quasar-cli/developing-pwa/configuring-pwa
    pwa: {
      workboxMode: 'generateSW', // or 'injectManifest'
      injectPwaMetaTags: true,
      swFilename: 'sw.js',
      manifestFilename: 'manifest.json',
      useCredentialsForManifestTag: false,
      // extendGenerateSWOptions (cfg) {}
      // extendInjectManifestOptions (cfg) {},
      // extendManifestJson (json) {}
      // extendPWACustomSWConf (esbuildConf) {}
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli/developing-cordova-apps/configuring-cordova
    cordova: {
      // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli/developing-capacitor-apps/configuring-capacitor
    capacitor: {
      hideSplashscreen: true,
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli/developing-electron-apps/configuring-electron
    electron: {
      // extendElectronMainConf (esbuildConf)
      // extendElectronPreloadConf (esbuildConf)

      inspectPort: 5858,

      bundler: 'packager', // 'packager' or 'builder'

      packager: {
        // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options
        // OS X / Mac App Store
        // appBundleId: '',
        // appCategoryType: '',
        // osxSign: '',
        // protocol: 'myapp://path',
        // Windows only
        // win32metadata: { ... }
      },

      builder: {
        // https://www.electron.build/configuration/configuration

        appId: 'q2',
      },
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-browser-extensions/configuring-bex
    bex: {
      contentScripts: ['my-content-script'],

      // extendBexScriptsConf (esbuildConf) {}
      // extendBexManifestJson (json) {}
    },
    // Width docker :https://quasar.dev/quasar-cli-webpack/quasar-config-file#docker-and-wsl-issues-with-hmr
    // extendWebpack(cfg) {
    //   cfg.watchOptions = {
    //     aggregateTimeout: 200,
    //     poll: 1000,
    //   }
    // },
  }
})
